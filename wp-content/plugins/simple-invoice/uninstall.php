<?php
/**
 * Uninstall WordPress Invoice Manager Pro Plugin
 *
 * This file is executed when the plugin is uninstalled (deleted).
 * It removes all plugin data including database tables and options.
 *
 * @package WPInvoiceManagerPro
 * @since 1.0.0
 */

// If uninstall not called from WordPress, then exit
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

// Include the functions file to access our helper functions
require_once plugin_dir_path(__FILE__) . 'includes/functions.php';

/**
 * Remove all plugin data on uninstall
 */
function wimp_uninstall_plugin() {
    global $wpdb;

    try {
        // Drop all plugin tables
        $tables = array(
            $wpdb->prefix . 'wimp_clients',
            $wpdb->prefix . 'wimp_templates',
            $wpdb->prefix . 'wimp_invoices'
        );

        foreach ($tables as $table) {
            $wpdb->query("DROP TABLE IF EXISTS {$table}");
        }

        // Remove all plugin options
        delete_option('wimp_settings');

        // Remove any transients
        delete_transient('wimp_plugin_version');

    } catch (Exception $e) {
        // Silent error handling
    }
}

// Execute the uninstall
wimp_uninstall_plugin();
