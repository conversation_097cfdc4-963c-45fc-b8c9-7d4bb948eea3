<?php
/**
 * Settings Page Template
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current settings
$settings = si_get_settings();

// Handle form submission
if (isset($_POST['save_settings']) && wp_verify_nonce($_POST['_wpnonce'], 'si_settings_nonce')) {

    $updated_settings = array(
        'business_name' => si_sanitize_text($_POST['business_name'] ?? ''),
        'business_address' => si_sanitize_textarea($_POST['business_address'] ?? ''),
        'business_email' => si_sanitize_email($_POST['business_email'] ?? ''),
        'business_phone' => si_sanitize_text($_POST['business_phone'] ?? ''),
        'business_logo' => si_sanitize_url($_POST['business_logo'] ?? ''),
        'gstin' => si_sanitize_text($_POST['gstin'] ?? ''),
        'default_due_days' => intval($_POST['default_due_days'] ?? 7),
        'payment_methods' => isset($_POST['payment_methods']) && is_array($_POST['payment_methods'])
            ? array_map('si_sanitize_text', $_POST['payment_methods'])
            : array(),
        'bank_details' => si_sanitize_textarea($_POST['bank_details'] ?? ''),
        'paypal_email' => si_sanitize_email($_POST['paypal_email'] ?? ''),
        'upi_id' => si_sanitize_text($_POST['upi_id'] ?? ''),
        'footer_notes' => si_sanitize_textarea($_POST['footer_notes'] ?? ''),
        'terms_text' => si_sanitize_textarea($_POST['terms_text'] ?? ''),
        'clear_data_on_deactivation' => isset($_POST['clear_data_on_deactivation']) ? 1 : 0
    );

    error_log('Updated settings array: ' . print_r($updated_settings, true));

    // Use the working si_update_settings function
    $result = si_update_settings($updated_settings);
    error_log('si_update_settings result: ' . var_export($result, true));

    if ($result) {
        echo '<div class="notice notice-success"><p>' . __('Settings saved successfully.', 'simple-invoice') . '</p></div>';

        // Force refresh the settings variable so form shows updated values
        // Get fresh settings data (bypass any caching)
        $settings = si_get_settings('', true);

        error_log('Settings saved successfully. Refreshed settings: ' . print_r($settings, true));

    } else {
        echo '<div class="notice notice-error"><p>' . __('Failed to save settings.', 'simple-invoice') . '</p></div>';
    }
} else {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        error_log('POST received but form submission not detected');
        error_log('save_settings isset: ' . (isset($_POST['save_settings']) ? 'yes' : 'no'));
        error_log('nonce verification: ' . (wp_verify_nonce($_POST['_wpnonce'] ?? '', 'si_settings_nonce') ? 'passed' : 'failed'));
    }
}




?>

<div class="wrap si-settings-wrap">
    <h1 class="wp-heading-inline"><?php echo esc_html__('Simple Invoice Settings', 'simple-invoice'); ?></h1>
    <a href="<?php echo esc_url(admin_url('admin.php?page=simple-invoice')); ?>" class="page-title-action">
        <span class="dashicons dashicons-arrow-left-alt"></span>
        <?php echo esc_html__('Back to Dashboard', 'simple-invoice'); ?>
    </a>

    <hr class="wp-header-end">

    <!-- Settings Layout Container -->
    <div class="si-settings-layout">
        <!-- Settings Content Area -->
        <div class="si-settings-content">
            <!-- Settings Navigation Tabs (Right Side) -->
            <div class="si-settings-nav">
                <nav class="nav-tab-wrapper">
                    <a href="#business-info" class="nav-tab nav-tab-active" data-tab="business-info">
                        <span class="dashicons dashicons-building"></span>
                        <span class="nav-tab-text"><?php echo esc_html__('Business Info', 'simple-invoice'); ?></span>
                    </a>
                    <a href="#invoice-settings" class="nav-tab" data-tab="invoice-settings">
                        <span class="dashicons dashicons-media-text"></span>
                        <span class="nav-tab-text"><?php echo esc_html__('Invoice Settings', 'simple-invoice'); ?></span>
                    </a>
                    <a href="#payment-settings" class="nav-tab" data-tab="payment-settings">
                        <span class="dashicons dashicons-money-alt"></span>
                        <span class="nav-tab-text"><?php echo esc_html__('Payment Settings', 'simple-invoice'); ?></span>
                    </a>
                    <a href="#footer-settings" class="nav-tab" data-tab="footer-settings">
                        <span class="dashicons dashicons-editor-alignleft"></span>
                        <span class="nav-tab-text"><?php echo esc_html__('Footer & Terms', 'simple-invoice'); ?></span>
                    </a>
                    <a href="#clear-data" class="nav-tab" data-tab="clear-data">
                        <span class="dashicons dashicons-trash"></span>
                        <span class="nav-tab-text"><?php echo esc_html__('Clear Data', 'simple-invoice'); ?></span>
                    </a>

                </nav>
            </div>

            <!-- Main Content Area -->
            <div class="si-settings-main">



                <form method="post" action="" class="si-settings-form">
                    <?php wp_nonce_field('si_settings_nonce'); ?>

                    <div class="si-settings-container">

            <!-- Business Information Tab -->
            <div class="si-settings-tab si-tab-content" id="business-info">
                <div class="si-settings-section">
                    <div class="si-section-header">
                        <h2>
                            <span class="dashicons dashicons-building"></span>
                            <?php echo esc_html__('Business Information', 'simple-invoice'); ?>
                        </h2>
                        <p class="si-section-description">
                            <?php echo esc_html__('Configure your business details that will appear on invoices and communications.', 'simple-invoice'); ?>
                        </p>
                    </div>
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="business_name"><?php echo esc_html__('Business Name', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="text" 
                                   id="business_name" 
                                   name="business_name" 
                                   value="<?php echo esc_attr($settings['business_name'] ?? ''); ?>" 
                                   class="regular-text" 
                                   placeholder="<?php echo esc_attr__('Enter your business name', 'simple-invoice'); ?>" />
                            <p class="description"><?php echo esc_html__('This will appear on your invoices.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="business_address"><?php echo esc_html__('Business Address', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <textarea id="business_address" 
                                      name="business_address" 
                                      rows="4" 
                                      class="large-text" 
                                      placeholder="<?php echo esc_attr__('Enter your complete business address', 'simple-invoice'); ?>"><?php echo esc_textarea($settings['business_address'] ?? ''); ?></textarea>
                            <p class="description"><?php echo esc_html__('Your business address for invoices.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="business_email"><?php echo esc_html__('Business Email', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="email" 
                                   id="business_email" 
                                   name="business_email" 
                                   value="<?php echo esc_attr($settings['business_email'] ?? ''); ?>" 
                                   class="regular-text" 
                                   placeholder="<?php echo esc_attr__('<EMAIL>', 'simple-invoice'); ?>" />
                            <p class="description"><?php echo esc_html__('Contact email for your business.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="business_phone"><?php echo esc_html__('Business Phone', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="text" 
                                   id="business_phone" 
                                   name="business_phone" 
                                   value="<?php echo esc_attr($settings['business_phone'] ?? ''); ?>" 
                                   class="regular-text" 
                                   placeholder="<?php echo esc_attr__('+****************', 'simple-invoice'); ?>" />
                            <p class="description"><?php echo esc_html__('Contact phone number for your business.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="business_logo"><?php echo esc_html__('Business Logo', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="url" 
                                   id="business_logo" 
                                   name="business_logo" 
                                   value="<?php echo esc_attr($settings['business_logo'] ?? ''); ?>" 
                                   class="regular-text si-media-input" 
                                   placeholder="<?php echo esc_attr__('Logo URL', 'simple-invoice'); ?>" />
                            <button type="button" class="button si-media-button" data-target="business_logo">
                                <?php echo esc_html__('Select Logo', 'simple-invoice'); ?>
                            </button>
                            <p class="description"><?php echo esc_html__('Upload or select your business logo. Leave empty to use site logo.', 'simple-invoice'); ?></p>
                            <?php if (!empty($settings['business_logo'])): ?>
                                <div class="si-logo-preview">
                                    <img src="<?php echo esc_url($settings['business_logo']); ?>" alt="<?php echo esc_attr__('Business Logo', 'simple-invoice'); ?>" style="max-width: 200px; height: auto;" />
                                </div>
                            <?php endif; ?>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="gstin"><?php echo esc_html__('GSTIN / Tax ID', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="text" 
                                   id="gstin" 
                                   name="gstin" 
                                   value="<?php echo esc_attr($settings['gstin'] ?? ''); ?>" 
                                   class="regular-text" 
                                   placeholder="<?php echo esc_attr__('Enter your GSTIN or Tax ID', 'simple-invoice'); ?>" />
                            <p class="description"><?php echo esc_html__('Your business tax identification number.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                </table>
                </div>
            </div>

            <!-- Invoice Settings Tab -->
            <div class="si-settings-tab si-tab-content" id="invoice-settings" style="display: none;">
                <div class="si-settings-section">
                    <div class="si-section-header">
                        <h2>
                            <span class="dashicons dashicons-media-text"></span>
                            <?php echo esc_html__('Invoice Settings', 'simple-invoice'); ?>
                        </h2>
                        <p class="si-section-description">
                            <?php echo esc_html__('Configure default settings for your invoices and billing preferences.', 'simple-invoice'); ?>
                        </p>
                    </div>
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="default_due_days"><?php echo esc_html__('Default Due Days', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="number" 
                                   id="default_due_days" 
                                   name="default_due_days" 
                                   value="<?php echo esc_attr($settings['default_due_days'] ?? 7); ?>" 
                                   class="small-text" 
                                   min="1" 
                                   max="365" />
                            <p class="description"><?php echo esc_html__('Number of days after invoice date when payment is due.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                </table>
                </div>
            </div>

            <!-- Payment Settings Tab -->
            <div class="si-settings-tab si-tab-content" id="payment-settings" style="display: none;">
                <div class="si-settings-section">
                    <div class="si-section-header">
                        <h2>
                            <span class="dashicons dashicons-money-alt"></span>
                            <?php echo esc_html__('Payment Settings', 'simple-invoice'); ?>
                        </h2>
                        <p class="si-section-description">
                            <?php echo esc_html__('Set up your payment methods and banking information for client payments.', 'simple-invoice'); ?>
                        </p>
                    </div>
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php echo esc_html__('Accepted Payment Methods', 'simple-invoice'); ?></th>
                        <td>
                            <fieldset>
                                <legend class="screen-reader-text"><?php echo esc_html__('Payment Methods', 'simple-invoice'); ?></legend>
                                
                                <label>
                                    <input type="checkbox"
                                           id="bank_enabled"
                                           name="payment_methods[]"
                                           value="bank"
                                           <?php checked(in_array('bank', $settings['payment_methods'] ?? array())); ?> />
                                    <?php echo esc_html__('Bank Transfer', 'simple-invoice'); ?>
                                </label><br />

                                <label>
                                    <input type="checkbox"
                                           id="paypal_enabled"
                                           name="payment_methods[]"
                                           value="paypal"
                                           <?php checked(in_array('paypal', $settings['payment_methods'] ?? array())); ?> />
                                    <?php echo esc_html__('PayPal', 'simple-invoice'); ?>
                                </label><br />

                                <label>
                                    <input type="checkbox"
                                           id="upi_enabled"
                                           name="payment_methods[]"
                                           value="upi"
                                           <?php checked(in_array('upi', $settings['payment_methods'] ?? array())); ?> />
                                    <?php echo esc_html__('UPI Payment', 'simple-invoice'); ?>
                                </label>
                            </fieldset>
                            <p class="description"><?php echo esc_html__('Select the payment methods you accept.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                    
                    <tr id="bank_details_row" style="display: <?php echo in_array('bank', $settings['payment_methods'] ?? array()) ? 'table-row' : 'none'; ?>;">
                        <th scope="row">
                            <label for="bank_details"><?php echo esc_html__('Bank Details', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <textarea id="bank_details"
                                      name="bank_details"
                                      rows="4"
                                      class="large-text"
                                      placeholder="<?php echo esc_attr__('Bank Name: XYZ Bank\nAccount Number: **********\nIFSC Code: ABCD0123456\nAccount Holder: Your Business Name', 'simple-invoice'); ?>"><?php echo esc_textarea($settings['bank_details'] ?? ''); ?></textarea>
                            <p class="description"><?php echo esc_html__('Your bank account details for wire transfers.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                    
                    <tr id="paypal_email_row" style="display: <?php echo in_array('paypal', $settings['payment_methods'] ?? array()) ? 'table-row' : 'none'; ?>;">
                        <th scope="row">
                            <label for="paypal_email"><?php echo esc_html__('PayPal Email', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="email"
                                   id="paypal_email"
                                   name="paypal_email"
                                   value="<?php echo esc_attr($settings['paypal_email'] ?? ''); ?>"
                                   class="regular-text"
                                   placeholder="<?php echo esc_attr__('<EMAIL>', 'simple-invoice'); ?>" />
                            <p class="description"><?php echo esc_html__('Your PayPal email address for receiving payments.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>

                    <tr id="upi_id_row" style="display: <?php echo in_array('upi', $settings['payment_methods'] ?? array()) ? 'table-row' : 'none'; ?>;">
                        <th scope="row">
                            <label for="upi_id"><?php echo esc_html__('UPI ID', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="text"
                                   id="upi_id"
                                   name="upi_id"
                                   value="<?php echo esc_attr($settings['upi_id'] ?? ''); ?>"
                                   class="regular-text"
                                   placeholder="<?php echo esc_attr__('yourname@paytm', 'simple-invoice'); ?>" />
                            <p class="description"><?php echo esc_html__('Your UPI ID for generating QR codes on invoices.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                </table>
                </div>
            </div>

            <!-- Footer Settings Tab -->
            <div class="si-settings-tab si-tab-content" id="footer-settings" style="display: none;">
                <div class="si-settings-section">
                    <div class="si-section-header">
                        <h2>
                            <span class="dashicons dashicons-editor-alignleft"></span>
                            <?php echo esc_html__('Footer & Terms', 'simple-invoice'); ?>
                        </h2>
                        <p class="si-section-description">
                            <?php echo esc_html__('Customize footer messages and terms & conditions for your invoices.', 'simple-invoice'); ?>
                        </p>
                    </div>
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="footer_notes"><?php echo esc_html__('Footer Notes', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <textarea id="footer_notes" 
                                      name="footer_notes" 
                                      rows="3" 
                                      class="large-text" 
                                      placeholder="<?php echo esc_attr__('Thank you for your business!', 'simple-invoice'); ?>"><?php echo esc_textarea($settings['footer_notes'] ?? ''); ?></textarea>
                            <p class="description"><?php echo esc_html__('Thank you message or additional notes to display at the bottom of invoices.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="terms_text"><?php echo esc_html__('Terms & Conditions', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <textarea id="terms_text" 
                                      name="terms_text" 
                                      rows="4" 
                                      class="large-text" 
                                      placeholder="<?php echo esc_attr__('Payment is due within the specified due date. Late payments may incur additional charges.', 'simple-invoice'); ?>"><?php echo esc_textarea($settings['terms_text'] ?? ''); ?></textarea>
                            <p class="description"><?php echo esc_html__('Terms and conditions or refund policy text.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                </table>
                </div>
            </div>

            <!-- Clear Data Tab -->
            <div class="si-settings-tab si-tab-content" id="clear-data" style="display: none;">
                <div class="si-settings-section">
                    <div class="si-section-header">
                        <h2>
                            <span class="dashicons dashicons-trash"></span>
                            <?php echo esc_html__('Clear Data', 'simple-invoice'); ?>
                        </h2>
                        <p class="si-section-description">
                            <?php echo esc_html__('Manage plugin data cleanup and reset options. Use these options carefully as they cannot be undone.', 'simple-invoice'); ?>
                        </p>
                    </div>

                    <?php
                    // Get current data counts
                    $data_counts = si_get_data_counts();
                    global $wpdb;
                    $custom_templates_count = (int) $wpdb->get_var(
                        $wpdb->prepare(
                            "SELECT COUNT(*) FROM {$wpdb->prefix}si_templates WHERE name != %s",
                            'Default Template'
                        )
                    );
                    ?>

                    <!-- Current Data Overview -->
                    <div class="si-data-overview">
                        <h3><?php echo esc_html__('Current Data Overview', 'simple-invoice'); ?></h3>
                        <div class="si-data-stats">
                            <div class="si-data-stat">
                                <div class="si-stat-icon si-stat-invoices">
                                    <span class="dashicons dashicons-media-text"></span>
                                </div>
                                <div class="si-stat-info">
                                    <span class="si-stat-number"><?php echo esc_html($data_counts['invoices']); ?></span>
                                    <span class="si-stat-label"><?php echo esc_html__('Invoices', 'simple-invoice'); ?></span>
                                </div>
                            </div>

                            <div class="si-data-stat">
                                <div class="si-stat-icon si-stat-clients">
                                    <span class="dashicons dashicons-groups"></span>
                                </div>
                                <div class="si-stat-info">
                                    <span class="si-stat-number"><?php echo esc_html($data_counts['clients']); ?></span>
                                    <span class="si-stat-label"><?php echo esc_html__('Clients', 'simple-invoice'); ?></span>
                                </div>
                            </div>

                            <div class="si-data-stat">
                                <div class="si-stat-icon si-stat-templates">
                                    <span class="dashicons dashicons-admin-appearance"></span>
                                </div>
                                <div class="si-stat-info">
                                    <span class="si-stat-number"><?php echo esc_html($data_counts['templates']); ?></span>
                                    <span class="si-stat-label"><?php echo esc_html__('Total Templates', 'simple-invoice'); ?></span>
                                </div>
                            </div>

                            <div class="si-data-stat">
                                <div class="si-stat-icon si-stat-custom">
                                    <span class="dashicons dashicons-admin-customizer"></span>
                                </div>
                                <div class="si-stat-info">
                                    <span class="si-stat-number"><?php echo esc_html($custom_templates_count); ?></span>
                                    <span class="si-stat-label"><?php echo esc_html__('Custom Templates', 'simple-invoice'); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Clear Data on Deactivation -->
                    <div class="si-clear-section">
                        <h3><?php echo esc_html__('Automatic Data Cleanup', 'simple-invoice'); ?></h3>
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="clear_data_on_deactivation"><?php echo esc_html__('Clear Data on Plugin Deactivation', 'simple-invoice'); ?></label>
                                </th>
                                <td>
                                    <label>
                                        <input type="checkbox"
                                               id="clear_data_on_deactivation"
                                               name="clear_data_on_deactivation"
                                               value="1"
                                               <?php checked($settings['clear_data_on_deactivation'] ?? 0, 1); ?> />
                                        <?php echo esc_html__('Automatically clear all plugin data when the plugin is deactivated', 'simple-invoice'); ?>
                                    </label>
                                    <p class="description">
                                        <strong style="color: #dc3545;"><?php echo esc_html__('Warning:', 'simple-invoice'); ?></strong>
                                        <?php echo esc_html__('When enabled, all invoices, clients, templates, and settings will be permanently deleted when you deactivate the plugin. This action cannot be undone.', 'simple-invoice'); ?>
                                    </p>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <!-- Selective Data Removal -->
                    <div class="si-clear-section">
                        <h3><?php echo esc_html__('Selective Data Removal', 'simple-invoice'); ?></h3>
                        <p class="si-section-description">
                            <?php echo esc_html__('Choose specific data types to remove from your database. This action is immediate and cannot be undone.', 'simple-invoice'); ?>
                        </p>

                        <div class="si-clear-warning">
                            <span class="dashicons dashicons-warning"></span>
                            <div>
                                <strong><?php echo esc_html__('Important:', 'simple-invoice'); ?></strong>
                                <?php echo esc_html__('These actions will permanently delete data from your database. Please make sure you have a backup before proceeding.', 'simple-invoice'); ?>
                            </div>
                        </div>

                        <div class="si-clear-checkboxes">
                            <div class="si-clear-option <?php echo $data_counts['invoices'] == 0 ? 'si-option-disabled' : ''; ?>">
                                <label class="si-clear-label">
                                    <input type="checkbox" name="clear_data_types[]" value="invoices" <?php echo $data_counts['invoices'] == 0 ? 'disabled' : ''; ?> />
                                    <div class="si-option-content">
                                        <strong>
                                            <?php echo esc_html__('Invoices', 'simple-invoice'); ?>
                                            <span class="si-count-badge"><?php echo esc_html($data_counts['invoices']); ?></span>
                                        </strong>
                                        <div class="si-option-description">
                                            <?php if ($data_counts['invoices'] > 0): ?>
                                                <?php echo esc_html(sprintf(__('Remove all %d generated invoices and their associated data', 'simple-invoice'), $data_counts['invoices'])); ?>
                                            <?php else: ?>
                                                <?php echo esc_html__('No invoices found to remove', 'simple-invoice'); ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </label>
                            </div>

                            <div class="si-clear-option <?php echo $data_counts['clients'] == 0 ? 'si-option-disabled' : ''; ?>">
                                <label class="si-clear-label">
                                    <input type="checkbox" name="clear_data_types[]" value="clients" <?php echo $data_counts['clients'] == 0 ? 'disabled' : ''; ?> />
                                    <div class="si-option-content">
                                        <strong>
                                            <?php echo esc_html__('Clients', 'simple-invoice'); ?>
                                            <span class="si-count-badge"><?php echo esc_html($data_counts['clients']); ?></span>
                                        </strong>
                                        <div class="si-option-description">
                                            <?php if ($data_counts['clients'] > 0): ?>
                                                <?php echo esc_html(sprintf(__('Remove all %d client records and their contact details', 'simple-invoice'), $data_counts['clients'])); ?>
                                            <?php else: ?>
                                                <?php echo esc_html__('No clients found to remove', 'simple-invoice'); ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </label>
                            </div>

                            <div class="si-clear-option <?php echo $data_counts['templates'] == 0 ? 'si-option-disabled' : ''; ?>">
                                <label class="si-clear-label">
                                    <input type="checkbox" name="clear_data_types[]" value="templates" <?php echo $data_counts['templates'] == 0 ? 'disabled' : ''; ?> />
                                    <div class="si-option-content">
                                        <strong>
                                            <?php echo esc_html__('All Design Templates', 'simple-invoice'); ?>
                                            <span class="si-count-badge"><?php echo esc_html($data_counts['templates']); ?></span>
                                        </strong>
                                        <div class="si-option-description">
                                            <?php if ($data_counts['templates'] > 0): ?>
                                                <?php echo esc_html(sprintf(__('Remove all %d design templates (default template will be recreated)', 'simple-invoice'), $data_counts['templates'])); ?>
                                            <?php else: ?>
                                                <?php echo esc_html__('No templates found to remove', 'simple-invoice'); ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </label>
                            </div>

                            <div class="si-clear-option <?php echo $custom_templates_count == 0 ? 'si-option-disabled' : ''; ?>">
                                <label class="si-clear-label">
                                    <input type="checkbox" name="clear_data_types[]" value="custom_templates" <?php echo $custom_templates_count == 0 ? 'disabled' : ''; ?> />
                                    <div class="si-option-content">
                                        <strong>
                                            <?php echo esc_html__('Custom Templates Only', 'simple-invoice'); ?>
                                            <span class="si-count-badge"><?php echo esc_html($custom_templates_count); ?></span>
                                        </strong>
                                        <div class="si-option-description">
                                            <?php if ($custom_templates_count > 0): ?>
                                                <?php echo esc_html(sprintf(__('Remove only %d custom-created templates, keeping the default template', 'simple-invoice'), $custom_templates_count)); ?>
                                            <?php else: ?>
                                                <?php echo esc_html__('No custom templates found to remove', 'simple-invoice'); ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <div class="si-clear-actions">
                            <div class="si-clear-buttons">
                                <button type="button" id="si-clear-data-btn" class="button" disabled>
                                    <?php echo esc_html__('Clear Selected Data', 'simple-invoice'); ?>
                                </button>
                                <span class="si-clear-status"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Reset to Default Settings -->
                    <div class="si-clear-section">
                        <h3><?php echo esc_html__('Reset Settings', 'simple-invoice'); ?></h3>
                        <p class="si-section-description">
                            <?php echo esc_html__('Reset all plugin settings back to their default values. This will not affect your invoices, clients, or templates.', 'simple-invoice'); ?>
                        </p>

                        <div class="si-clear-actions">
                            <div class="si-clear-buttons">
                                <button type="button" id="si-reset-settings-btn" class="button">
                                    <?php echo esc_html__('Reset to Default Settings', 'simple-invoice'); ?>
                                </button>
                                <span class="si-reset-status"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>



                    <!-- Save Button (Fixed Position) - Only for main settings -->
                    <div class="si-settings-save" id="si-main-save-button">
                        <input type="submit" name="save_settings" id="si-save-settings" class="button button-primary" value="<?php echo esc_attr__('Save Settings', 'simple-invoice'); ?>" />
                        <span class="si-save-status"></span>
                    </div>
                </form>
            </div>
        </div>
    </div>


</div>

<style>
/* Settings Page Styling */
.si-settings-wrap {
    background: #ffffff;
    margin: 0 -20px;
    padding: 20px;
    min-height: 100vh;
}

.wp-heading-inline {
    color: #000000;
    font-size: 28px;
    font-weight: 700;
}

.page-title-action {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff !important;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.page-title-action:hover {
    background: linear-gradient(135deg, #5f5f5f 0%, #000000 100%);
    color: #ffffff !important;
    text-decoration: none;
    transform: translateY(-1px);
}

/* Settings Layout */
.si-settings-layout {
    display: flex;
    gap: 0;
    margin: 20px 0;
    min-height: 600px;
}

.si-settings-content {
    display: flex;
    flex: 1;
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    overflow: hidden;
}

.si-settings-main {
    flex: 1;
    background: #ffffff;
    order: 2;
}

/* Left Side Vertical Tab Styling */
.si-settings-nav {
    width: 280px;
    background: #f8f9fa;
    border-right: 1px solid #e7e7e7;
    order: 1;
    flex-shrink: 0;
}

.nav-tab-wrapper {
    display: flex;
    flex-direction: column;
    border: none;
    margin: 0;
    padding: 20px 0;
    height: 100%;
}

.nav-tab {
    background: transparent;
    color: #5f5f5f;
    border: none;
    border-right: 3px solid transparent;
    padding: 15px 20px;
    margin: 0 0 5px 0;
    border-radius: 0;
    font-weight: 600;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.3s ease;
    position: relative;
    width: 100%;
    box-sizing: border-box;
}

.nav-tab:hover {
    background: #ffffff;
    color: #f47a45;
    text-decoration: none;
    border-right-color: #f47a45;
}

.nav-tab-active,
.nav-tab-active:hover {
    background: #ffffff;
    color: #f47a45;
    border-right-color: #f47a45;
    font-weight: 700;
}

.nav-tab .dashicons {
    font-size: 18px;
    width: 18px;
    height: 18px;
    flex-shrink: 0;
}

.nav-tab-text {
    font-size: 14px;
}

/* Settings Container */
.si-settings-container {
    background: #ffffff;
    border: none;
    border-radius: 0;
    padding: 0;
    margin: 0;
    height: 100%;
}

/* Tab Content Display Fix - Remove !important from hide rule */
.si-settings-container .si-tab-content {
    display: none;
}

.si-settings-container .si-tab-content.si-tab-active {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Hide other tabs when not active */
.si-settings-container .si-tab-content:not(.si-tab-active) {
    display: none !important;
}

.si-settings-section {
    padding: 30px;
}

.si-section-header {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e7e7e7;
}

.si-section-header h2 {
    color: #000000;
    font-size: 22px;
    font-weight: 700;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.si-section-header .dashicons {
    color: #f47a45;
    font-size: 24px;
    width: 24px;
    height: 24px;
}

.si-section-description {
    color: #5f5f5f;
    font-size: 14px;
    margin: 0;
    line-height: 1.5;
}

/* Form Table Styling */
.form-table th {
    color: #000000;
    font-weight: 600;
    padding: 15px 10px 15px 0;
}

.form-table td {
    padding: 15px 10px;
}

.form-table input[type="text"],
.form-table input[type="email"],
.form-table input[type="url"],
.form-table input[type="number"],
.form-table textarea {
    border: 2px solid #e7e7e7;
    border-radius: 6px;
    padding: 10px 12px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-table input[type="text"]:focus,
.form-table input[type="email"]:focus,
.form-table input[type="url"]:focus,
.form-table input[type="number"]:focus,
.form-table textarea:focus {
    border-color: #f47a45;
    box-shadow: 0 0 0 1px #f47a45;
    outline: none;
}

.description {
    color: #5f5f5f;
    font-style: italic;
}



/* Save Button */
.si-settings-save {
    position: sticky;
    bottom: 0;
    background: #ffffff;
    padding: 20px 30px;
    border-top: 1px solid #e7e7e7;
    margin-top: auto;
    z-index: 100;
}

#si-save-settings {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    pointer-events: auto !important;
    z-index: 1001;
    position: relative;
}

#si-save-settings:hover {
    background: linear-gradient(135deg, #5f5f5f 0%, #000000 100%);
    transform: translateY(-2px);
}

#si-save-settings:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* Plugin Settings Section */
.si-plugin-settings-section {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 12px;
    margin-bottom: 20px;
}

.si-section-title-wrapper {
    display: flex;
    align-items: center;
    gap: 15px;
}

.si-section-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
}

.si-setting-card {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
}

.si-setting-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.si-setting-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
}

.si-setting-info h3 {
    color: #000000;
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 5px 0;
}

.si-setting-info p {
    color: #5f5f5f;
    font-size: 14px;
    margin: 0;
}

/* Toggle Switch */
.si-toggle-switch {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
}

.si-toggle-switch input[type="checkbox"] {
    display: none;
}

.si-toggle-slider {
    width: 50px;
    height: 26px;
    background: #e7e7e7;
    border-radius: 13px;
    position: relative;
    transition: background 0.3s ease;
}

.si-toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 22px;
    height: 22px;
    background: #ffffff;
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.si-toggle-switch input[type="checkbox"]:checked + .si-toggle-slider {
    background: #f47a45;
}

.si-toggle-switch input[type="checkbox"]:checked + .si-toggle-slider::before {
    transform: translateX(24px);
}

.si-toggle-label {
    color: #000000;
    font-weight: 500;
}

/* Warning Box */
.si-setting-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 12px;
    margin-top: 15px;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.si-warning-icon {
    color: #f47a45;
    flex-shrink: 0;
    margin-top: 2px;
}

.si-warning-content {
    color: #5f5f5f;
    font-size: 14px;
    line-height: 1.4;
}

.si-warning-content strong {
    color: #000000;
}

/* Plugin Settings Save Button */
.si-plugin-settings-save {
    padding: 20px 30px;
    border-top: 1px solid #e7e7e7;
    background: #ffffff;
    border-radius: 0 0 12px 12px;
}

.si-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.si-btn-primary {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
}

.si-btn-primary:hover {
    background: linear-gradient(135deg, #5f5f5f 0%, #000000 100%);
    transform: translateY(-2px);
}

/* Data Overview */
.si-data-overview {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.si-data-overview h3 {
    color: #000000;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 15px 0;
}

.si-data-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.si-data-stats .si-stat-item {
    background: #e7e7e7;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
}

.si-data-stats .si-stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #f47a45;
    display: block;
    margin-bottom: 5px;
}

.si-data-stats .si-stat-label {
    font-size: 12px;
    color: #5f5f5f;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Clear Data Options */
.si-clear-options {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.si-clear-options h3 {
    color: #000000;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 15px 0;
}

.si-clear-checkboxes {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin: 20px 0;
}

.si-clear-option {
    background: #ffffff;
    border: 2px solid #e7e7e7;
    border-radius: 8px;
    padding: 15px;
    transition: all 0.3s ease;
}

.si-clear-option:hover {
    border-color: #f47a45;
    box-shadow: 0 2px 8px rgba(244, 122, 69, 0.1);
}

.si-clear-label {
    display: flex;
    align-items: center;
    gap: 15px;
    cursor: pointer;
    margin: 0;
}

.si-clear-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #f47a45;
}

.si-option-content strong {
    color: #000000;
    font-size: 16px;
    font-weight: 600;
    display: block;
    margin-bottom: 5px;
}

.si-option-description {
    color: #5f5f5f;
    font-size: 14px;
}

.si-clear-actions {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.si-clear-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    color: #856404;
}

.si-clear-warning .dashicons {
    color: #f39c12;
    font-size: 20px;
}

.si-clear-buttons {
    display: flex;
    gap: 10px;
    align-items: center;
}

#si-clear-data-btn {
    background: #dc3545;
    color: #ffffff;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

#si-clear-data-btn:hover:not(:disabled) {
    background: #c82333;
    transform: translateY(-1px);
}

#si-clear-data-btn:disabled {
    background: #e7e7e7;
    color: #5f5f5f;
    cursor: not-allowed;
    transform: none;
}

#si-reset-settings-btn {
    background: #f47a45;
    color: #ffffff;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

#si-reset-settings-btn:hover:not(:disabled) {
    background: #e66a35;
    transform: translateY(-1px);
}

#si-reset-settings-btn:disabled {
    background: #e7e7e7;
    color: #5f5f5f;
    cursor: not-allowed;
    transform: none;
}

.si-clear-section {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid #e7e7e7;
}

.si-clear-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.si-clear-section h3 {
    color: #000000;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 15px 0;
    padding: 0;
}

.si-clear-status,
.si-reset-status {
    margin-left: 15px;
    font-weight: 600;
}

.si-clear-status.success,
.si-reset-status.success {
    color: #28a745;
}

.si-clear-status.error,
.si-reset-status.error {
    color: #dc3545;
}

.si-clear-status.processing,
.si-reset-status.processing {
    color: #f47a45;
}

.si-clear-status.warning,
.si-reset-status.warning {
    color: #ffc107;
}

/* Data Overview Styling */
.si-data-overview {
    background: #f8f9fa;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
}

.si-data-overview h3 {
    color: #000000;
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 15px 0;
    padding: 0;
}

.si-data-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.si-data-stat {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 6px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.3s ease;
}

.si-data-stat:hover {
    border-color: #f47a45;
    box-shadow: 0 2px 8px rgba(244, 122, 69, 0.1);
}

.si-stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.si-stat-icon .dashicons {
    font-size: 20px;
    width: 20px;
    height: 20px;
}

.si-stat-invoices {
    background: rgba(244, 122, 69, 0.1);
    color: #f47a45;
}

.si-stat-clients {
    background: rgba(95, 95, 95, 0.1);
    color: #5f5f5f;
}

.si-stat-templates {
    background: rgba(0, 123, 255, 0.1);
    color: #007bff;
}

.si-stat-custom {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.si-stat-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.si-stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #000000;
    line-height: 1;
}

.si-stat-label {
    font-size: 12px;
    color: #5f5f5f;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Count Badges */
.si-count-badge {
    background: #f47a45;
    color: #ffffff;
    font-size: 11px;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 12px;
    margin-left: 8px;
    display: inline-block;
    min-width: 20px;
    text-align: center;
}

/* Disabled Options */
.si-option-disabled {
    opacity: 0.5;
    pointer-events: none;
}

.si-option-disabled .si-count-badge {
    background: #e7e7e7;
    color: #5f5f5f;
}

.si-option-disabled .si-clear-label {
    cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
    .si-data-stats {
        grid-template-columns: 1fr;
    }

    .si-data-stat {
        padding: 12px;
    }

    .si-stat-icon {
        width: 35px;
        height: 35px;
    }

    .si-stat-number {
        font-size: 20px;
    }
}

#si-clear-data-btn:disabled {
    background: #e7e7e7;
    color: #5f5f5f;
    cursor: not-allowed;
    transform: none;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .si-settings-layout {
        flex-direction: column;
    }

    .si-settings-content {
        flex-direction: column;
    }

    .si-settings-nav {
        width: 100%;
        order: 1;
        border-right: none;
        border-bottom: 1px solid #e7e7e7;
    }

    .nav-tab-wrapper {
        flex-direction: row;
        padding: 15px 20px;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .nav-tab {
        flex-shrink: 0;
        white-space: nowrap;
        border-left: none;
        border-bottom: 3px solid transparent;
        padding: 12px 16px;
        margin: 0 5px 0 0;
        border-radius: 6px 6px 0 0;
    }

    .nav-tab:hover,
    .nav-tab-active {
        border-right: none;
        border-bottom-color: #f47a45;
    }

    .si-settings-main {
        order: 2;
    }
}

@media (max-width: 768px) {
    .si-settings-save {
        position: static;
        margin-top: 20px;
        padding: 15px 20px;
        border: 1px solid #e7e7e7;
    }

    .nav-tab {
        padding: 10px 12px;
        font-size: 13px;
    }

    .nav-tab-text {
        display: none;
    }

    .si-section-header h2 {
        font-size: 20px;
    }

    .si-setting-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    console.log('Simple Invoice Settings Page JavaScript loaded');

    // Tab functionality
    $('.nav-tab').on('click', function(e) {
        e.preventDefault();

        var targetTab = $(this).data('tab');
        console.log('Tab clicked:', targetTab);

        // Remove active class from all tabs
        $('.nav-tab').removeClass('nav-tab-active');
        $(this).addClass('nav-tab-active');

        // Hide all tab content properly
        $('.si-tab-content').removeClass('si-tab-active').hide();

        // Show target tab content
        var targetElement = $('#' + targetTab);
        targetElement.addClass('si-tab-active').show();

        console.log('Showing tab content for:', targetTab);
        console.log('Tab content element found:', targetElement.length);
        console.log('Tab content is visible:', targetElement.is(':visible'));

        // Show save button for all tabs
        $('#si-main-save-button').show();
        console.log('Save button shown for tab:', targetTab);

        // Update URL hash
        window.location.hash = targetTab;
    });

    // Check for hash on page load and activate appropriate tab
    if (window.location.hash) {
        var hash = window.location.hash.substring(1);
        console.log('Hash found on page load:', hash);
        var targetTab = $('.nav-tab[data-tab="' + hash + '"]');
        console.log('Target tab found:', targetTab.length);
        if (targetTab.length) {
            targetTab.trigger('click');
        }
    } else {
        // Default to business-info tab if no hash
        $('.nav-tab[data-tab="business-info"]').trigger('click');
    }



    // Initialize save button visibility
    $('#si-main-save-button').show();





    // Media uploader for logo
    $('.si-media-button').on('click', function(e) {
        e.preventDefault();

        var button = $(this);
        var targetInput = $('#' + button.data('target'));

        var mediaUploader = wp.media({
            title: '<?php echo esc_js(__('Select Business Logo', 'simple-invoice')); ?>',
            button: {
                text: '<?php echo esc_js(__('Use this image', 'simple-invoice')); ?>'
            },
            multiple: false
        });

        mediaUploader.on('select', function() {
            var attachment = mediaUploader.state().get('selection').first().toJSON();
            targetInput.val(attachment.url);

            // Update preview if exists
            var preview = targetInput.closest('td').find('.si-logo-preview');
            if (preview.length) {
                preview.find('img').attr('src', attachment.url);
            } else {
                targetInput.after('<div class="si-logo-preview"><img src="' + attachment.url + '" alt="<?php echo esc_attr__('Business Logo', 'simple-invoice'); ?>" style="max-width: 200px; height: auto; margin-top: 10px;" /></div>');
            }
        });

        mediaUploader.open();
    });

    // Payment method field toggles
    function initPaymentToggles() {
        console.log('Initializing payment toggles...');

        // Bank Transfer toggle
        $('#bank_enabled').on('change', function() {
            console.log('Bank checkbox changed:', $(this).is(':checked'));
            if ($(this).is(':checked')) {
                $('#bank_details_row').show().fadeIn(300);
            } else {
                $('#bank_details_row').fadeOut(300);
            }
        });

        // PayPal toggle
        $('#paypal_enabled').on('change', function() {
            console.log('PayPal checkbox changed:', $(this).is(':checked'));
            if ($(this).is(':checked')) {
                $('#paypal_email_row').show().fadeIn(300);
            } else {
                $('#paypal_email_row').fadeOut(300);
            }
        });

        // UPI toggle
        $('#upi_enabled').on('change', function() {
            console.log('UPI checkbox changed:', $(this).is(':checked'));
            if ($(this).is(':checked')) {
                $('#upi_id_row').show().fadeIn(300);
            } else {
                $('#upi_id_row').fadeOut(300);
            }
        });

        console.log('Payment toggles initialized');
    }

    // Initialize payment toggles
    initPaymentToggles();

    // Let the save button submit naturally - no JavaScript interference needed
    $('#si-save-settings').on('click', function(e) {
        console.log('Save button clicked - allowing natural form submission');
        // Don't prevent default - let the form submit naturally
        // The form will submit to the same page and PHP will handle it
    });

    // Auto-save indication
    $('.si-settings-form input, .si-settings-form textarea, .si-settings-form select').on('change', function() {
        $('.si-save-status').text('<?php echo esc_js(__('Unsaved changes', 'simple-invoice')); ?>').removeClass('success').addClass('warning');
    });

    // Clear Data functionality
    function initClearDataFunctionality() {
        // Enable/disable clear data button based on checkbox selection
        $('input[name="clear_data_types[]"]').on('change', function() {
            var checkedBoxes = $('input[name="clear_data_types[]"]:checked:not(:disabled)').length;
            $('#si-clear-data-btn').prop('disabled', checkedBoxes === 0);
        });

        // Clear selected data
        $('#si-clear-data-btn').on('click', function() {
            var selectedTypes = [];
            $('input[name="clear_data_types[]"]:checked').each(function() {
                selectedTypes.push($(this).val());
            });

            if (selectedTypes.length === 0) {
                alert('<?php echo esc_js(__('Please select at least one data type to clear.', 'simple-invoice')); ?>');
                return;
            }

            var confirmMessage = '<?php echo esc_js(__('Are you sure you want to permanently delete the selected data? This action cannot be undone.', 'simple-invoice')); ?>\n\n';
            confirmMessage += '<?php echo esc_js(__('Selected data types:', 'simple-invoice')); ?>\n';

            selectedTypes.forEach(function(type) {
                var label = $('input[value="' + type + '"]').closest('.si-clear-option').find('strong').text();
                confirmMessage += '- ' + label + '\n';
            });

            if (!confirm(confirmMessage)) {
                return;
            }

            // Show loading state
            var $button = $(this);
            var originalText = $button.text();
            $button.prop('disabled', true).text('<?php echo esc_js(__('Clearing...', 'simple-invoice')); ?>');
            $('.si-clear-status').text('<?php echo esc_js(__('Processing...', 'simple-invoice')); ?>').removeClass('success error').addClass('processing');

            // Send AJAX request
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'si_clear_selected_data',
                    nonce: '<?php echo wp_create_nonce('si_clear_data_nonce'); ?>',
                    clear_types: selectedTypes
                },
                success: function(response) {
                    if (response.success) {
                        $('.si-clear-status').text('<?php echo esc_js(__('Data cleared successfully! Refreshing page...', 'simple-invoice')); ?>').removeClass('processing error').addClass('success');
                        // Uncheck all checkboxes
                        $('input[name="clear_data_types[]"]').prop('checked', false);
                        $button.prop('disabled', true);

                        // Refresh the page after 2 seconds to update counts
                        setTimeout(function() {
                            window.location.reload();
                        }, 2000);
                    } else {
                        $('.si-clear-status').text(response.data || '<?php echo esc_js(__('Failed to clear data.', 'simple-invoice')); ?>').removeClass('processing success').addClass('error');
                    }
                },
                error: function() {
                    $('.si-clear-status').text('<?php echo esc_js(__('An error occurred while clearing data.', 'simple-invoice')); ?>').removeClass('processing success').addClass('error');
                },
                complete: function() {
                    $button.prop('disabled', false).text(originalText);
                    setTimeout(function() {
                        $('.si-clear-status').text('');
                    }, 5000);
                }
            });
        });

        // Reset settings to default
        $('#si-reset-settings-btn').on('click', function() {
            if (!confirm('<?php echo esc_js(__('Are you sure you want to reset all settings to their default values? This will not affect your invoices, clients, or templates.', 'simple-invoice')); ?>')) {
                return;
            }

            var $button = $(this);
            var originalText = $button.text();
            $button.prop('disabled', true).text('<?php echo esc_js(__('Resetting...', 'simple-invoice')); ?>');
            $('.si-reset-status').text('<?php echo esc_js(__('Processing...', 'simple-invoice')); ?>').removeClass('success error').addClass('processing');

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'si_reset_settings',
                    nonce: '<?php echo wp_create_nonce('si_reset_settings_nonce'); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        $('.si-reset-status').text('<?php echo esc_js(__('Settings reset successfully! Reloading page...', 'simple-invoice')); ?>').removeClass('processing error').addClass('success');
                        setTimeout(function() {
                            window.location.reload();
                        }, 2000);
                    } else {
                        $('.si-reset-status').text(response.data || '<?php echo esc_js(__('Failed to reset settings.', 'simple-invoice')); ?>').removeClass('processing success').addClass('error');
                    }
                },
                error: function() {
                    $('.si-reset-status').text('<?php echo esc_js(__('An error occurred while resetting settings.', 'simple-invoice')); ?>').removeClass('processing success').addClass('error');
                },
                complete: function() {
                    $button.prop('disabled', false).text(originalText);
                    setTimeout(function() {
                        $('.si-reset-status').text('');
                    }, 5000);
                }
            });
        });
    }

    // Initialize clear data functionality
    initClearDataFunctionality();

});
</script>
