<?php
/**
 * Classic Invoice Design Template
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Extract variables for use in template
$business_name = $settings['business_name'] ?? get_bloginfo('name');
$business_logo = $settings['business_logo'] ?? '';
$business_address = $settings['business_address'] ?? '';
$business_email = $settings['business_email'] ?? '';
$business_phone = $settings['business_phone'] ?? '';
$gstin = $settings['gstin'] ?? '';
$upi_id = $settings['upi_id'] ?? '';

$client_name = $client->name ?? '';
$client_business = $client->business_name ?? '';
$client_address = $client->address ?? '';
$client_email = $client->email ?? '';
$client_phone = $client->contact_number ?? '';
$client_gstin = $client->gstin ?? '';

$invoice_items = $items ?? array();
$invoice_subtotal = $subtotal ?? 0;
$invoice_tax_rate = $tax_rate ?? 0;
$invoice_tax_amount = $tax_amount ?? 0;
$invoice_discount = $discount_amount ?? 0;
$invoice_shipping = $shipping_amount ?? 0;
$invoice_total = $total_amount ?? 0;
$invoice_notes = $notes ?? '';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo esc_html__('Invoice', 'simple-invoice'); ?> - <?php echo esc_html($invoice_number); ?></title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            color: #000000;
            line-height: 1.6;
            background: #ffffff;
        }

        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: #ffffff;
            padding: 40px;
            border: 1px solid #e7e7e7;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #f47a45;
        }
        
        .business-info {
            flex: 1;
        }
        
        .business-logo {
            margin-bottom: 15px;
        }
        
        .business-logo img {
            max-width: 200px;
            height: auto;
        }
        
        .business-name {
            font-size: 28px;
            font-weight: bold;
            color: #f47a45;
            margin: 0 0 10px 0;
        }

        .business-details {
            font-size: 14px;
            color: #5f5f5f;
            line-height: 1.5;
        }
        
        .invoice-title {
            text-align: right;
            flex: 0 0 auto;
        }
        
        .invoice-title h1 {
            font-size: 36px;
            color: #f47a45;
            margin: 0;
            font-weight: bold;
        }
        
        .invoice-meta {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        
        .invoice-details,
        .client-details {
            flex: 1;
        }
        
        .invoice-details {
            margin-right: 40px;
        }
        
        .detail-group {
            margin-bottom: 20px;
        }
        
        .detail-group h3 {
            font-size: 16px;
            color: #000000;
            margin: 0 0 10px 0;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .detail-item {
            margin-bottom: 5px;
            font-size: 14px;
        }
        
        .detail-label {
            font-weight: bold;
            color: #000000;
            display: inline-block;
            width: 120px;
        }

        .detail-value {
            color: #5f5f5f;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            background: #ffffff;
        }

        .items-table th {
            background: #e7e7e7;
            color: #000000;
            font-weight: bold;
            padding: 15px 10px;
            text-align: left;
            border-bottom: 2px solid #f47a45;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .items-table td {
            padding: 12px 10px;
            border-bottom: 1px solid #e7e7e7;
            font-size: 14px;
        }

        .items-table tr:nth-child(even) {
            background: #e7e7e7;
        }
        
        .items-table .text-right {
            text-align: right;
        }
        
        .items-table .text-center {
            text-align: center;
        }
        
        .summary-section {
            margin-top: 30px;
            display: flex;
            justify-content: flex-end;
        }
        
        .summary-table {
            width: 300px;
            border-collapse: collapse;
        }
        
        .summary-table td {
            padding: 8px 15px;
            border-bottom: 1px solid #e7e7e7;
            font-size: 14px;
        }

        .summary-table .summary-label {
            font-weight: bold;
            color: #000000;
            text-align: right;
            width: 60%;
        }

        .summary-table .summary-value {
            text-align: right;
            color: #5f5f5f;
            width: 40%;
        }
        
        .summary-table .total-row {
            border-top: 2px solid #f47a45;
            border-bottom: 3px double #f47a45;
        }

        .summary-table .total-row td {
            font-size: 16px;
            font-weight: bold;
            color: #f47a45;
            padding: 12px 15px;
        }
        
        .payment-info {
            margin-top: 40px;
            padding: 20px;
            background: #e7e7e7;
            border-left: 4px solid #f47a45;
        }
        
        .payment-info h3 {
            margin: 0 0 15px 0;
            font-size: 16px;
            color: #000000;
            font-weight: bold;
        }
        
        .payment-methods {
            display: flex;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .payment-method {
            flex: 1;
            min-width: 200px;
        }
        
        .payment-method h4 {
            margin: 0 0 8px 0;
            font-size: 14px;
            color: #000000;
            font-weight: bold;
        }

        .payment-method p {
            margin: 0;
            font-size: 13px;
            color: #5f5f5f;
            line-height: 1.4;
        }
        
        .upi-qr {
            text-align: center;
            margin-top: 20px;
        }
        
        .upi-qr img {
            max-width: 150px;
            height: auto;
            border: 1px solid #e7e7e7;
            padding: 10px;
            background: #ffffff;
        }

        .upi-qr p {
            margin: 10px 0 0 0;
            font-size: 12px;
            color: #5f5f5f;
        }
        
        .notes-section {
            margin-top: 30px;
            padding: 20px;
            background: #e7e7e7;
            border-radius: 4px;
        }

        .notes-section h3 {
            margin: 0 0 10px 0;
            font-size: 16px;
            color: #000000;
            font-weight: bold;
        }

        .notes-section p {
            margin: 0;
            font-size: 14px;
            color: #5f5f5f;
            line-height: 1.5;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e7e7e7;
            text-align: center;
            font-size: 12px;
            color: #5f5f5f;
        }
        
        @media print {
            body {
                padding: 0;
            }
            
            .invoice-container {
                box-shadow: none;
                border: none;
                padding: 20px;
            }
            
            .no-print {
                display: none !important;
            }
        }
        
        @media (max-width: 600px) {
            .invoice-header {
                flex-direction: column;
                text-align: center;
            }
            
            .invoice-title {
                text-align: center;
                margin-top: 20px;
            }
            
            .invoice-meta {
                flex-direction: column;
            }
            
            .invoice-details {
                margin-right: 0;
                margin-bottom: 20px;
            }
            
            .payment-methods {
                flex-direction: column;
            }
            
            .summary-section {
                justify-content: center;
            }
            
            .summary-table {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Header -->
        <div class="invoice-header">
            <div class="business-info">
                <?php if (!empty($business_logo)): ?>
                    <div class="business-logo">
                        <img src="<?php echo esc_url($business_logo); ?>" alt="<?php echo esc_attr($business_name); ?>" />
                    </div>
                <?php endif; ?>
                
                <h2 class="business-name"><?php echo esc_html($business_name); ?></h2>
                
                <div class="business-details">
                    <?php if (!empty($business_address)): ?>
                        <div><?php echo nl2br(esc_html($business_address)); ?></div>
                    <?php endif; ?>
                    
                    <?php if (!empty($business_email)): ?>
                        <div><?php echo esc_html__('Email:', 'simple-invoice'); ?> <?php echo esc_html($business_email); ?></div>
                    <?php endif; ?>
                    
                    <?php if (!empty($business_phone)): ?>
                        <div><?php echo esc_html__('Phone:', 'simple-invoice'); ?> <?php echo esc_html($business_phone); ?></div>
                    <?php endif; ?>
                    
                    <?php if (!empty($gstin)): ?>
                        <div><?php echo esc_html__('GSTIN:', 'simple-invoice'); ?> <?php echo esc_html($gstin); ?></div>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="invoice-title">
                <h1><?php echo esc_html__('INVOICE', 'simple-invoice'); ?></h1>
            </div>
        </div>
        
        <!-- Invoice and Client Details -->
        <div class="invoice-meta">
            <div class="invoice-details">
                <div class="detail-group">
                    <h3><?php echo esc_html__('Invoice Details', 'simple-invoice'); ?></h3>
                    <div class="detail-item">
                        <span class="detail-label"><?php echo esc_html__('Invoice #:', 'simple-invoice'); ?></span>
                        <span class="detail-value"><?php echo esc_html($invoice_number); ?></span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label"><?php echo esc_html__('Date:', 'simple-invoice'); ?></span>
                        <span class="detail-value"><?php echo esc_html($invoice_date); ?></span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label"><?php echo esc_html__('Due Date:', 'simple-invoice'); ?></span>
                        <span class="detail-value"><?php echo esc_html($due_date); ?></span>
                    </div>
                </div>
            </div>
            
            <div class="client-details">
                <div class="detail-group">
                    <h3><?php echo esc_html__('Bill To', 'simple-invoice'); ?></h3>
                    <div class="detail-item">
                        <strong><?php echo esc_html($client_name); ?></strong>
                    </div>
                    
                    <?php if (!empty($client_business)): ?>
                        <div class="detail-item">
                            <?php echo esc_html($client_business); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($client_address)): ?>
                        <div class="detail-item">
                            <?php echo nl2br(esc_html($client_address)); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($client_email)): ?>
                        <div class="detail-item">
                            <?php echo esc_html($client_email); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($client_phone)): ?>
                        <div class="detail-item">
                            <?php echo esc_html($client_phone); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($client_gstin)): ?>
                        <div class="detail-item">
                            <?php echo esc_html__('GSTIN:', 'simple-invoice'); ?> <?php echo esc_html($client_gstin); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th><?php echo esc_html__('Description', 'simple-invoice'); ?></th>
                    <th class="text-center"><?php echo esc_html__('Qty', 'simple-invoice'); ?></th>
                    <th class="text-right"><?php echo esc_html__('Rate', 'simple-invoice'); ?></th>
                    <th class="text-right"><?php echo esc_html__('Amount', 'simple-invoice'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($invoice_items)): ?>
                    <?php foreach ($invoice_items as $item): ?>
                        <tr>
                            <td><?php echo esc_html($item['description'] ?? $item[1] ?? ''); ?></td>
                            <td class="text-center"><?php echo esc_html($item['quantity'] ?? $item[2] ?? ''); ?></td>
                            <td class="text-right"><?php echo si_format_currency($item['rate'] ?? $item[3] ?? 0); ?></td>
                            <td class="text-right"><?php echo si_format_currency(($item['quantity'] ?? $item[2] ?? 0) * ($item['rate'] ?? $item[3] ?? 0)); ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="4" style="text-align: center; color: #999; font-style: italic;">
                            <?php echo esc_html__('No items found.', 'simple-invoice'); ?>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
        
        <!-- Summary -->
        <div class="summary-section">
            <table class="summary-table">
                <tr>
                    <td class="summary-label"><?php echo esc_html__('Subtotal:', 'simple-invoice'); ?></td>
                    <td class="summary-value"><?php echo si_format_currency($invoice_subtotal); ?></td>
                </tr>
                
                <?php if ($invoice_tax_rate > 0): ?>
                    <tr>
                        <td class="summary-label"><?php echo esc_html__('Tax', 'simple-invoice'); ?> (<?php echo esc_html($invoice_tax_rate); ?>%):</td>
                        <td class="summary-value"><?php echo si_format_currency($invoice_tax_amount); ?></td>
                    </tr>
                <?php endif; ?>
                
                <?php if ($invoice_discount > 0): ?>
                    <tr>
                        <td class="summary-label"><?php echo esc_html__('Discount:', 'simple-invoice'); ?></td>
                        <td class="summary-value">-<?php echo si_format_currency($invoice_discount); ?></td>
                    </tr>
                <?php endif; ?>
                
                <?php if ($invoice_shipping > 0): ?>
                    <tr>
                        <td class="summary-label"><?php echo esc_html__('Shipping:', 'simple-invoice'); ?></td>
                        <td class="summary-value"><?php echo si_format_currency($invoice_shipping); ?></td>
                    </tr>
                <?php endif; ?>
                
                <tr class="total-row">
                    <td class="summary-label"><?php echo esc_html__('Total:', 'simple-invoice'); ?></td>
                    <td class="summary-value"><?php echo si_format_currency($invoice_total); ?></td>
                </tr>
            </table>
        </div>
        
        <!-- Payment Information -->
        <?php if (!empty($settings['payment_methods'])): ?>
            <div class="payment-info">
                <h3><?php echo esc_html__('Payment Information', 'simple-invoice'); ?></h3>
                
                <div class="payment-methods">
                    <?php if (in_array('bank', $settings['payment_methods']) && !empty($settings['bank_details'])): ?>
                        <div class="payment-method">
                            <h4><?php echo esc_html__('Bank Transfer', 'simple-invoice'); ?></h4>
                            <p><?php echo nl2br(esc_html($settings['bank_details'])); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (in_array('upi', $settings['payment_methods']) && !empty($upi_id)): ?>
                        <div class="payment-method">
                            <h4><?php echo esc_html__('UPI Payment', 'simple-invoice'); ?></h4>
                            <p><?php echo esc_html__('UPI ID:', 'simple-invoice'); ?> <?php echo esc_html($upi_id); ?></p>
                            
                            <?php if (!empty($upi_qr_code)): ?>
                                <div class="upi-qr">
                                    <img src="<?php echo esc_url($upi_qr_code); ?>" alt="<?php echo esc_attr__('UPI QR Code', 'simple-invoice'); ?>" />
                                    <p><?php echo esc_html__('Scan to pay', 'simple-invoice'); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Notes -->
        <?php if (!empty($invoice_notes) || !empty($settings['footer_notes']) || !empty($settings['terms_text'])): ?>
            <div class="notes-section">
                <?php if (!empty($invoice_notes)): ?>
                    <h3><?php echo esc_html__('Notes', 'simple-invoice'); ?></h3>
                    <p><?php echo nl2br(esc_html($invoice_notes)); ?></p>
                <?php endif; ?>
                
                <?php if (!empty($settings['footer_notes'])): ?>
                    <h3><?php echo esc_html__('Thank You', 'simple-invoice'); ?></h3>
                    <p><?php echo nl2br(esc_html($settings['footer_notes'])); ?></p>
                <?php endif; ?>
                
                <?php if (!empty($settings['terms_text'])): ?>
                    <h3><?php echo esc_html__('Terms & Conditions', 'simple-invoice'); ?></h3>
                    <p><?php echo nl2br(esc_html($settings['terms_text'])); ?></p>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        
        <!-- Footer -->
        <div class="footer">
            <p><?php echo esc_html__('This is a computer-generated invoice.', 'simple-invoice'); ?></p>
        </div>
    </div>
</body>
</html>
