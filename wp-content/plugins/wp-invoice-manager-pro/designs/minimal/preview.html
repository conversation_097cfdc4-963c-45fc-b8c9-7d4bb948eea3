<!DOCTYPE html>
<html>
<head>
    <title>Minimal Design Preview</title>
    <style>
        body { font-family: 'Helvetica Neue', sans-serif; margin: 20px; background: #f5f5f5; }
        .preview { width: 300px; height: 200px; background: white; padding: 20px; border: 1px solid #ecf0f1; }
        .header { border-bottom: 1px solid #ecf0f1; padding-bottom: 10px; margin-bottom: 15px; }
        .title { font-size: 20px; font-weight: 100; letter-spacing: 2px; color: #34495e; }
        .business { font-size: 12px; color: #7f8c8d; font-weight: 300; }
        .content { font-size: 11px; color: #2c3e50; }
        .table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .table th { color: #95a5a6; padding: 4px 0; border-bottom: 1px solid #ecf0f1; font-size: 9px; font-weight: 600; text-transform: uppercase; }
        .table td { padding: 3px 0; border-bottom: 1px solid #ecf0f1; font-size: 9px; }
        .total { text-align: right; font-weight: 600; margin-top: 8px; border-top: 1px solid #ecf0f1; padding-top: 8px; }
    </style>
</head>
<body>
    <div class="preview">
        <div class="header">
            <div class="title">INVOICE</div>
            <div class="business">Minimal Business</div>
        </div>
        <div class="content">
            <table class="table">
                <tr><th>Item</th><th>Qty</th><th>Rate</th><th>Total</th></tr>
                <tr><td>Service</td><td>1</td><td>$100</td><td>$100</td></tr>
                <tr><td>Product</td><td>2</td><td>$50</td><td>$100</td></tr>
            </table>
            <div class="total">Total: $200</div>
        </div>
    </div>
</body>
</html>
