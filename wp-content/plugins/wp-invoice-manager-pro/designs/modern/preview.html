<!DOCTYPE html>
<html>
<head>
    <title>Modern Design Preview</title>
    <style>
        body { font-family: 'Inter', sans-serif; margin: 20px; background: #f5f5f5; }
        .preview { width: 300px; height: 200px; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 8px rgba(0,0,0,0.15); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; }
        .title { font-size: 20px; font-weight: 300; letter-spacing: 2px; }
        .business { font-size: 12px; opacity: 0.9; }
        .content { padding: 15px; font-size: 11px; }
        .table { width: 100%; border-collapse: collapse; margin: 8px 0; }
        .table th { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 4px; font-size: 9px; }
        .table td { padding: 3px 4px; border-bottom: 1px solid #e5e7eb; font-size: 9px; }
        .total { text-align: right; font-weight: bold; margin-top: 8px; color: #1f2937; }
    </style>
</head>
<body>
    <div class="preview">
        <div class="header">
            <div class="title">INVOICE</div>
            <div class="business">Modern Business</div>
        </div>
        <div class="content">
            <table class="table">
                <tr><th>Item</th><th>Qty</th><th>Rate</th><th>Total</th></tr>
                <tr><td>Service</td><td>1</td><td>$100</td><td>$100</td></tr>
                <tr><td>Product</td><td>2</td><td>$50</td><td>$100</td></tr>
            </table>
            <div class="total">Total: $200</div>
        </div>
    </div>
</body>
</html>
