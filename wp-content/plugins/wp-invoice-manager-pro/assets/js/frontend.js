/**
 * Simple Invoice Frontend JavaScript
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Frontend functionality for Simple Invoice
    var SI_Frontend = {
        init: function() {
            this.bindEvents();
        },

        bindEvents: function() {
            // Print functionality
            $(document).on('click', '.si-print-invoice', this.printInvoice);
            
            // Download functionality
            $(document).on('click', '.si-download-invoice', this.downloadInvoice);
        },

        printInvoice: function(e) {
            e.preventDefault();
            
            var invoiceContent = $('.si-invoice-preview').html();
            var printWindow = window.open('', '_blank');
            
            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Invoice</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        table { width: 100%; border-collapse: collapse; }
                        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
                        th { background-color: #f2f2f2; }
                        .text-right { text-align: right; }
                        .text-center { text-align: center; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    ${invoiceContent}
                </body>
                </html>
            `);
            
            printWindow.document.close();
            printWindow.focus();
            printWindow.print();
            printWindow.close();
        },

        downloadInvoice: function(e) {
            e.preventDefault();
            
            var button = $(this);
            var invoiceId = button.data('invoice-id');
            
            if (invoiceId) {
                var downloadUrl = button.attr('href');
                window.location.href = downloadUrl;
            }
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        SI_Frontend.init();
    });

})(jQuery);
