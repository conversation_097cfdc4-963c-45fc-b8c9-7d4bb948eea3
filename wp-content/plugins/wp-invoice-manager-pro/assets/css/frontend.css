/**
 * Simple Invoice Frontend Styles
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

/* Frontend styles for any public-facing elements */
.si-invoice-preview {
    max-width: 800px;
    margin: 0 auto;
    background: #ffffff;
    padding: 20px;
    border: 1px solid #e7e7e7;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.si-invoice-preview h1,
.si-invoice-preview h2,
.si-invoice-preview h3 {
    color: #000000;
    margin-top: 0;
}

.si-invoice-preview table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
}

.si-invoice-preview th,
.si-invoice-preview td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #e7e7e7;
}

.si-invoice-preview th {
    background: #e7e7e7;
    font-weight: bold;
}

.si-invoice-preview .text-right {
    text-align: right;
}

.si-invoice-preview .text-center {
    text-align: center;
}

/* Print styles */
@media print {
    .si-invoice-preview {
        box-shadow: none;
        border: none;
        padding: 0;
    }
    
    .no-print {
        display: none !important;
    }
}
