<?php
/**
 * Template Management Class
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * SI_Template class for managing invoice templates
 *
 * @since 1.0.0
 */
class SI_Template {

    /**
     * Table name
     *
     * @var string
     * @since 1.0.0
     */
    private $table_name;

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'si_templates';
        
        // Initialize hooks
        $this->si_init_hooks();
    }

    /**
     * Initialize hooks
     *
     * @since 1.0.0
     */
    private function si_init_hooks() {
        add_action('wp_ajax_si_add_template', array($this, 'si_ajax_add_template'));
        add_action('wp_ajax_si_edit_template', array($this, 'si_ajax_edit_template'));
        add_action('wp_ajax_si_delete_template', array($this, 'si_ajax_delete_template'));
        add_action('wp_ajax_si_get_template', array($this, 'si_ajax_get_template'));
    }

    /**
     * Create a new template
     *
     * @param array $data Template data
     * @return int|false Template ID on success, false on failure
     * @since 1.0.0
     */
    public function si_create_template($data) {
        global $wpdb;

        // Sanitize data
        $sanitized_data = $this->si_sanitize_template_data($data);

        // Validate required fields
        if (empty($sanitized_data['name'])) {
            return false;
        }

        // Check if template name already exists
        $existing = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT id FROM $this->table_name WHERE name = %s",
                $sanitized_data['name']
            )
        );

        if ($existing) {
            return false; // Template name already exists
        }

        // Insert template
        $result = $wpdb->insert(
            $this->table_name,
            $sanitized_data,
            array('%s', '%s', '%s', '%s', '%s')
        );

        return $result ? $wpdb->insert_id : false;
    }

    /**
     * Update an existing template
     *
     * @param int $template_id Template ID
     * @param array $data Template data
     * @return bool True on success, false on failure
     * @since 1.0.0
     */
    public function si_update_template($template_id, $data) {
        global $wpdb;

        // Sanitize data
        $sanitized_data = $this->si_sanitize_template_data($data);

        // Validate required fields
        if (empty($sanitized_data['name'])) {
            return false;
        }

        // Check if template name already exists (excluding current template)
        $existing = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT id FROM $this->table_name WHERE name = %s AND id != %d",
                $sanitized_data['name'],
                $template_id
            )
        );

        if ($existing) {
            return false; // Template name already exists
        }

        // Update template
        $result = $wpdb->update(
            $this->table_name,
            $sanitized_data,
            array('id' => $template_id),
            array('%s', '%s', '%s', '%s', '%s'),
            array('%d')
        );

        return $result !== false;
    }

    /**
     * Delete a template
     *
     * @param int $template_id Template ID
     * @return bool True on success, false on failure
     * @since 1.0.0
     */
    public function si_delete_template($template_id) {
        global $wpdb;

        // Check if template has invoices
        $invoice_table = $wpdb->prefix . 'si_invoices';
        $invoice_count = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM $invoice_table WHERE template_id = %d",
                $template_id
            )
        );

        if ($invoice_count > 0) {
            return false; // Cannot delete template with existing invoices
        }

        // Delete template
        $result = $wpdb->delete(
            $this->table_name,
            array('id' => $template_id),
            array('%d')
        );

        return $result !== false;
    }

    /**
     * Get template by ID
     *
     * @param int $template_id Template ID
     * @return object|null Template object or null if not found
     * @since 1.0.0
     */
    public function si_get_template($template_id) {
        global $wpdb;

        $template = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $this->table_name WHERE id = %d",
                $template_id
            )
        );

        if ($template) {
            // Decode JSON fields
            $template->header_fields = json_decode($template->header_fields, true) ?: array();
            $template->body_fields = json_decode($template->body_fields, true) ?: array();
            $template->summary_fields = json_decode($template->summary_fields, true) ?: array();
        }

        return $template;
    }

    /**
     * Get all templates
     *
     * @param array $args Query arguments
     * @return array Array of template objects
     * @since 1.0.0
     */
    public function si_get_templates($args = array()) {
        global $wpdb;

        $defaults = array(
            'search' => '',
            'orderby' => 'name',
            'order' => 'ASC',
            'limit' => -1,
            'offset' => 0
        );

        $args = wp_parse_args($args, $defaults);

        $sql = "SELECT * FROM $this->table_name";
        $where_conditions = array();

        // Add search condition
        if (!empty($args['search'])) {
            $search = '%' . $wpdb->esc_like($args['search']) . '%';
            $where_conditions[] = $wpdb->prepare(
                "name LIKE %s",
                $search
            );
        }

        // Add WHERE clause if conditions exist
        if (!empty($where_conditions)) {
            $sql .= " WHERE " . implode(' AND ', $where_conditions);
        }

        // Add ORDER BY clause
        $sql .= " ORDER BY " . esc_sql($args['orderby']) . " " . esc_sql($args['order']);

        // Add LIMIT clause
        if ($args['limit'] > 0) {
            $sql .= " LIMIT " . intval($args['offset']) . ", " . intval($args['limit']);
        }

        $templates = $wpdb->get_results($sql);

        // Decode JSON fields for each template
        foreach ($templates as $template) {
            $template->header_fields = json_decode($template->header_fields, true) ?: array();
            $template->body_fields = json_decode($template->body_fields, true) ?: array();
            $template->summary_fields = json_decode($template->summary_fields, true) ?: array();
        }

        return $templates;
    }

    /**
     * Get default template fields
     *
     * @return array Default template structure
     * @since 1.0.0
     */
    public function si_get_default_template_fields() {
        return array(
            'header_fields' => array(
                'business_name' => true,
                'business_logo' => true,
                'business_address' => true,
                'business_email' => true,
                'business_phone' => true,
                'gstin' => true
            ),
            'body_fields' => array(
                array(
                    'label' => 'Sr No.',
                    'type' => 'serial',
                    'required' => true,
                    'editable' => false
                ),
                array(
                    'label' => 'Product/Service',
                    'type' => 'text',
                    'required' => true,
                    'editable' => true
                ),
                array(
                    'label' => 'Quantity',
                    'type' => 'number',
                    'required' => true,
                    'editable' => true
                ),
                array(
                    'label' => 'Rate',
                    'type' => 'currency',
                    'required' => true,
                    'editable' => true
                ),
                array(
                    'label' => 'Total',
                    'type' => 'calculated',
                    'formula' => 'quantity * rate',
                    'required' => true,
                    'editable' => false
                )
            ),
            'summary_fields' => array(
                array(
                    'label' => 'Subtotal',
                    'type' => 'calculated',
                    'formula' => 'sum_total',
                    'required' => true
                ),
                array(
                    'label' => 'Tax (%)',
                    'type' => 'percentage',
                    'required' => false
                ),
                array(
                    'label' => 'Discount',
                    'type' => 'currency',
                    'required' => false
                ),
                array(
                    'label' => 'Shipping',
                    'type' => 'currency',
                    'required' => false
                ),
                array(
                    'label' => 'Total Amount',
                    'type' => 'calculated',
                    'formula' => 'subtotal + tax - discount + shipping',
                    'required' => true
                )
            )
        );
    }

    /**
     * Sanitize template data
     *
     * @param array $data Raw template data
     * @return array Sanitized template data
     * @since 1.0.0
     */
    private function si_sanitize_template_data($data) {
        $sanitized = array(
            'name' => si_sanitize_text($data['name'] ?? ''),
            'design' => si_sanitize_text($data['design'] ?? 'classic')
        );

        // Sanitize and encode JSON fields
        $sanitized['header_fields'] = wp_json_encode($data['header_fields'] ?? array());
        $sanitized['body_fields'] = wp_json_encode($data['body_fields'] ?? array());
        $sanitized['summary_fields'] = wp_json_encode($data['summary_fields'] ?? array());

        return $sanitized;
    }

    /**
     * AJAX handler for adding template
     *
     * @since 1.0.0
     */
    public function si_ajax_add_template() {
        // Verify nonce
        if (!si_verify_ajax_nonce('si_add_template_nonce')) {
            si_send_json_response(false, __('Security check failed.', 'simple-invoice'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            si_send_json_response(false, __('Insufficient permissions.', 'simple-invoice'));
        }

        // Create template
        $template_id = $this->si_create_template($_POST);

        if ($template_id) {
            $template = $this->si_get_template($template_id);
            si_send_json_response(
                true,
                __('Template added successfully.', 'simple-invoice'),
                array('template' => $template)
            );
        } else {
            si_send_json_response(false, __('Failed to add template. Name may already exist.', 'simple-invoice'));
        }
    }

    /**
     * AJAX handler for editing template
     *
     * @since 1.0.0
     */
    public function si_ajax_edit_template() {
        // Verify nonce
        if (!si_verify_ajax_nonce('si_edit_template_nonce')) {
            si_send_json_response(false, __('Security check failed.', 'simple-invoice'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            si_send_json_response(false, __('Insufficient permissions.', 'simple-invoice'));
        }

        $template_id = intval($_POST['template_id'] ?? 0);

        if (!$template_id) {
            si_send_json_response(false, __('Invalid template ID.', 'simple-invoice'));
        }

        // Update template
        $success = $this->si_update_template($template_id, $_POST);

        if ($success) {
            $template = $this->si_get_template($template_id);
            si_send_json_response(
                true,
                __('Template updated successfully.', 'simple-invoice'),
                array('template' => $template)
            );
        } else {
            si_send_json_response(false, __('Failed to update template. Name may already exist.', 'simple-invoice'));
        }
    }

    /**
     * AJAX handler for deleting template
     *
     * @since 1.0.0
     */
    public function si_ajax_delete_template() {
        // Verify nonce
        if (!si_verify_ajax_nonce('si_delete_template_nonce')) {
            si_send_json_response(false, __('Security check failed.', 'simple-invoice'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            si_send_json_response(false, __('Insufficient permissions.', 'simple-invoice'));
        }

        $template_id = intval($_POST['template_id'] ?? 0);

        if (!$template_id) {
            si_send_json_response(false, __('Invalid template ID.', 'simple-invoice'));
        }

        // Delete template
        $success = $this->si_delete_template($template_id);

        if ($success) {
            si_send_json_response(true, __('Template deleted successfully.', 'simple-invoice'));
        } else {
            si_send_json_response(false, __('Failed to delete template. Template may have existing invoices.', 'simple-invoice'));
        }
    }

    /**
     * AJAX handler for getting template data
     *
     * @since 1.0.0
     */
    public function si_ajax_get_template() {
        // Verify nonce
        if (!si_verify_ajax_nonce('si_get_template_nonce')) {
            si_send_json_response(false, __('Security check failed.', 'simple-invoice'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            si_send_json_response(false, __('Insufficient permissions.', 'simple-invoice'));
        }

        $template_id = intval($_POST['template_id'] ?? 0);

        if (!$template_id) {
            si_send_json_response(false, __('Invalid template ID.', 'simple-invoice'));
        }

        $template = $this->si_get_template($template_id);

        if ($template) {
            si_send_json_response(
                true,
                __('Template data retrieved.', 'simple-invoice'),
                array('template' => $template)
            );
        } else {
            si_send_json_response(false, __('Template not found.', 'simple-invoice'));
        }
    }
}
