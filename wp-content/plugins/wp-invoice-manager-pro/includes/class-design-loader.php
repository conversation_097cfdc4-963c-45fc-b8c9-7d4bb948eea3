<?php
/**
 * Design Loader Class
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * SI_Design_Loader class for managing invoice designs
 *
 * @since 1.0.0
 */
class SI_Design_Loader {

    /**
     * Designs directory path
     *
     * @var string
     * @since 1.0.0
     */
    private $designs_path;

    /**
     * Designs directory URL
     *
     * @var string
     * @since 1.0.0
     */
    private $designs_url;

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        $this->designs_path = SI_PLUGIN_PATH . 'designs/';
        $this->designs_url = SI_PLUGIN_URL . 'designs/';
        
        // Initialize hooks
        $this->si_init_hooks();
    }

    /**
     * Initialize hooks
     *
     * @since 1.0.0
     */
    private function si_init_hooks() {
        add_action('wp_ajax_si_get_design_preview', array($this, 'si_ajax_get_design_preview'));
    }

    /**
     * Get all available designs
     *
     * @return array Available designs
     * @since 1.0.0
     */
    public function si_get_available_designs() {
        $designs = array();
        
        if (!is_dir($this->designs_path)) {
            return $designs;
        }

        $design_dirs = scandir($this->designs_path);
        
        foreach ($design_dirs as $dir) {
            if ($dir === '.' || $dir === '..' || !is_dir($this->designs_path . $dir)) {
                continue;
            }

            $template_file = $this->designs_path . $dir . '/template.php';
            $preview_file = $this->designs_path . $dir . '/preview.jpg';
            
            if (file_exists($template_file)) {
                $designs[$dir] = array(
                    'id' => $dir,
                    'name' => $this->si_format_design_name($dir),
                    'template_path' => $template_file,
                    'preview_url' => file_exists($preview_file) ? $this->designs_url . $dir . '/preview.jpg' : '',
                    'description' => $this->si_get_design_description($dir)
                );
            }
        }
        
        return $designs;
    }

    /**
     * Get design by ID
     *
     * @param string $design_id Design ID
     * @return array|null Design data or null if not found
     * @since 1.0.0
     */
    public function si_get_design($design_id) {
        $designs = $this->si_get_available_designs();
        return isset($designs[$design_id]) ? $designs[$design_id] : null;
    }

    /**
     * Load design template
     *
     * @param string $design_id Design ID
     * @param array $data Invoice data
     * @return string Rendered HTML
     * @since 1.0.0
     */
    public function si_load_design_template($design_id, $data = array()) {
        $design = $this->si_get_design($design_id);
        
        if (!$design || !file_exists($design['template_path'])) {
            return $this->si_get_fallback_template($data);
        }

        // Start output buffering
        ob_start();
        
        // Extract data for use in template
        extract($data);
        
        // Include the design template
        include $design['template_path'];
        
        // Get the rendered content
        $content = ob_get_clean();
        
        return $content;
    }

    /**
     * Get design CSS
     *
     * @param string $design_id Design ID
     * @return string CSS content
     * @since 1.0.0
     */
    public function si_get_design_css($design_id) {
        $css_file = $this->designs_path . $design_id . '/style.css';
        
        if (file_exists($css_file)) {
            return file_get_contents($css_file);
        }
        
        return $this->si_get_default_css();
    }

    /**
     * Format design name for display
     *
     * @param string $design_id Design ID
     * @return string Formatted name
     * @since 1.0.0
     */
    private function si_format_design_name($design_id) {
        return ucwords(str_replace(array('-', '_'), ' ', $design_id));
    }

    /**
     * Get design description
     *
     * @param string $design_id Design ID
     * @return string Design description
     * @since 1.0.0
     */
    private function si_get_design_description($design_id) {
        $descriptions = array(
            'classic' => __('A traditional and professional invoice design with clean lines and standard formatting.', 'simple-invoice'),
            'modern' => __('A contemporary design with modern typography and sleek visual elements.', 'simple-invoice'),
            'minimal' => __('A clean and minimalist design focusing on simplicity and readability.', 'simple-invoice')
        );
        
        return isset($descriptions[$design_id]) ? $descriptions[$design_id] : __('Custom invoice design template.', 'simple-invoice');
    }

    /**
     * Get fallback template when design is not found
     *
     * @param array $data Invoice data
     * @return string Fallback HTML template
     * @since 1.0.0
     */
    private function si_get_fallback_template($data) {
        $settings = si_get_settings();
        
        ob_start();
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title><?php echo esc_html__('Invoice', 'simple-invoice'); ?></title>
            <style>
                <?php echo $this->si_get_default_css(); ?>
            </style>
        </head>
        <body>
            <div class="invoice-container">
                <div class="invoice-header">
                    <h1><?php echo esc_html($settings['business_name'] ?? get_bloginfo('name')); ?></h1>
                    <?php if (!empty($settings['business_address'])): ?>
                        <p><?php echo nl2br(esc_html($settings['business_address'])); ?></p>
                    <?php endif; ?>
                </div>
                
                <div class="invoice-details">
                    <h2><?php echo esc_html__('Invoice', 'simple-invoice'); ?></h2>
                    <p><strong><?php echo esc_html__('Invoice Number:', 'simple-invoice'); ?></strong> <?php echo esc_html($data['invoice_number'] ?? ''); ?></p>
                    <p><strong><?php echo esc_html__('Date:', 'simple-invoice'); ?></strong> <?php echo esc_html(date('Y-m-d')); ?></p>
                </div>
                
                <div class="client-details">
                    <h3><?php echo esc_html__('Bill To:', 'simple-invoice'); ?></h3>
                    <?php if (isset($data['client'])): ?>
                        <p><strong><?php echo esc_html($data['client']['name']); ?></strong></p>
                        <?php if (!empty($data['client']['address'])): ?>
                            <p><?php echo nl2br(esc_html($data['client']['address'])); ?></p>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
                
                <div class="invoice-items">
                    <table>
                        <thead>
                            <tr>
                                <th><?php echo esc_html__('Description', 'simple-invoice'); ?></th>
                                <th><?php echo esc_html__('Quantity', 'simple-invoice'); ?></th>
                                <th><?php echo esc_html__('Rate', 'simple-invoice'); ?></th>
                                <th><?php echo esc_html__('Total', 'simple-invoice'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (isset($data['items']) && is_array($data['items'])): ?>
                                <?php foreach ($data['items'] as $item): ?>
                                    <tr>
                                        <td><?php echo esc_html($item['description'] ?? ''); ?></td>
                                        <td><?php echo esc_html($item['quantity'] ?? ''); ?></td>
                                        <td><?php echo esc_html($item['rate'] ?? ''); ?></td>
                                        <td><?php echo esc_html($item['total'] ?? ''); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                
                <div class="invoice-total">
                    <p><strong><?php echo esc_html__('Total Amount:', 'simple-invoice'); ?> <?php echo esc_html($data['total_amount'] ?? '0.00'); ?></strong></p>
                </div>
            </div>
        </body>
        </html>
        <?php
        return ob_get_clean();
    }

    /**
     * Get default CSS styles
     *
     * @return string Default CSS
     * @since 1.0.0
     */
    private function si_get_default_css() {
        return '
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                color: #000000;
            }
            .invoice-container {
                max-width: 800px;
                margin: 0 auto;
                background: #ffffff;
                padding: 30px;
                border: 1px solid #e7e7e7;
            }
            .invoice-header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #f47a45;
                padding-bottom: 20px;
            }
            .invoice-header h1 {
                margin: 0;
                font-size: 28px;
                color: #f47a45;
            }
            .invoice-details, .client-details {
                margin-bottom: 30px;
            }
            .invoice-items table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 30px;
            }
            .invoice-items th,
            .invoice-items td {
                padding: 12px;
                text-align: left;
                border-bottom: 1px solid #e7e7e7;
            }
            .invoice-items th {
                background-color: #e7e7e7;
                font-weight: bold;
            }
            .invoice-total {
                text-align: right;
                font-size: 18px;
                margin-top: 20px;
                color: #f47a45;
                font-weight: bold;
                padding-top: 10px;
                border-top: 2px solid #f47a45;
            }
        ';
    }

    /**
     * AJAX handler for getting design preview
     *
     * @since 1.0.0
     */
    public function si_ajax_get_design_preview() {
        // Verify nonce
        if (!si_verify_ajax_nonce('si_design_preview_nonce')) {
            si_send_json_response(false, __('Security check failed.', 'simple-invoice'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            si_send_json_response(false, __('Insufficient permissions.', 'simple-invoice'));
        }

        $design_id = si_sanitize_text($_POST['design_id'] ?? '');

        if (empty($design_id)) {
            si_send_json_response(false, __('Invalid design ID.', 'simple-invoice'));
        }

        $design = $this->si_get_design($design_id);

        if ($design) {
            si_send_json_response(
                true,
                __('Design preview retrieved.', 'simple-invoice'),
                array('design' => $design)
            );
        } else {
            si_send_json_response(false, __('Design not found.', 'simple-invoice'));
        }
    }

    /**
     * Validate design template
     *
     * @param string $design_id Design ID
     * @return bool True if valid, false otherwise
     * @since 1.0.0
     */
    public function si_validate_design($design_id) {
        $design = $this->si_get_design($design_id);
        return $design !== null;
    }

    /**
     * Get design template variables
     *
     * @param string $design_id Design ID
     * @return array Available template variables
     * @since 1.0.0
     */
    public function si_get_design_variables($design_id) {
        return array(
            'settings' => 'Plugin settings array',
            'client' => 'Client information object',
            'template' => 'Template configuration object',
            'invoice_number' => 'Generated invoice number',
            'invoice_date' => 'Invoice creation date',
            'due_date' => 'Payment due date',
            'items' => 'Array of invoice items',
            'subtotal' => 'Subtotal amount',
            'tax_amount' => 'Tax amount',
            'discount_amount' => 'Discount amount',
            'shipping_amount' => 'Shipping amount',
            'total_amount' => 'Final total amount',
            'upi_qr_code' => 'UPI QR code data (if enabled)'
        );
    }
}
