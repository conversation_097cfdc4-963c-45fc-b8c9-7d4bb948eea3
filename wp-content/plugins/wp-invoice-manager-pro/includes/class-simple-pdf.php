<?php
/**
 * Simple PDF Generator Class
 * 
 * A lightweight PDF generator for Simple Invoice plugin
 * 
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Simple PDF Generator Class
 */
class SI_Simple_PDF {
    
    private $objects = array();
    private $current_object = 0;
    private $pages = array();
    private $current_page = 0;
    private $fonts = array();
    
    public function __construct() {
        $this->init();
    }
    
    private function init() {
        // Initialize basic PDF structure
        $this->add_object(); // Object 1: Catalog
        $this->add_object(); // Object 2: Pages
        $this->add_page();
    }
    
    private function add_object() {
        $this->current_object++;
        $this->objects[$this->current_object] = '';
        return $this->current_object;
    }
    
    public function add_page() {
        $this->current_page++;
        $this->pages[$this->current_page] = array(
            'content' => '',
            'y_position' => 750
        );
    }
    
    public function add_text($text, $x = 50, $y = null, $font_size = 12) {
        if ($y === null) {
            $y = $this->pages[$this->current_page]['y_position'];
        }

        // Check if we need a new page
        if ($y < 50) {
            $this->add_page();
            $y = 750;
        }

        // Escape text for PDF
        $text = $this->escape_text($text);

        // Add text to current page with proper PDF syntax
        $this->pages[$this->current_page]['content'] .= "BT\n";
        $this->pages[$this->current_page]['content'] .= "/F1 {$font_size} Tf\n";
        $this->pages[$this->current_page]['content'] .= "1 0 0 1 {$x} {$y} Tm\n";
        $this->pages[$this->current_page]['content'] .= "({$text}) Tj\n";
        $this->pages[$this->current_page]['content'] .= "ET\n";

        // Update Y position
        $this->pages[$this->current_page]['y_position'] = $y - ($font_size + 5);
    }
    
    public function add_line_break($height = 15) {
        $this->pages[$this->current_page]['y_position'] -= $height;
    }
    
    public function add_title($text, $font_size = 18) {
        $this->add_text($text, 50, null, $font_size);
        $this->add_line_break(25);
    }
    
    public function add_heading($text, $font_size = 14) {
        $this->add_text($text, 50, null, $font_size);
        $this->add_line_break(20);
    }
    
    public function add_paragraph($text, $font_size = 10) {
        // Split long text into multiple lines
        $lines = $this->wrap_text($text, 80);
        foreach ($lines as $line) {
            $this->add_text($line, 50, null, $font_size);
            $this->add_line_break(12);
        }
        $this->add_line_break(5);
    }
    
    public function add_table_row($columns, $font_size = 10) {
        $x_positions = array(50, 200, 350, 450);
        $y = $this->pages[$this->current_page]['y_position'];
        
        for ($i = 0; $i < count($columns) && $i < 4; $i++) {
            $this->add_text($columns[$i], $x_positions[$i], $y, $font_size);
        }
        
        $this->add_line_break(15);
    }
    
    private function wrap_text($text, $max_length) {
        $words = explode(' ', $text);
        $lines = array();
        $current_line = '';
        
        foreach ($words as $word) {
            if (strlen($current_line . ' ' . $word) <= $max_length) {
                $current_line .= ($current_line ? ' ' : '') . $word;
            } else {
                if ($current_line) {
                    $lines[] = $current_line;
                }
                $current_line = $word;
            }
        }
        
        if ($current_line) {
            $lines[] = $current_line;
        }
        
        return $lines;
    }
    
    private function escape_text($text) {
        $text = str_replace('\\', '\\\\', $text);
        $text = str_replace('(', '\\(', $text);
        $text = str_replace(')', '\\)', $text);
        $text = preg_replace('/[^\x20-\x7E]/', '', $text);
        return $text;
    }
    
    public function output($filename = 'document.pdf', $destination = 'D') {
        $pdf_content = $this->build_pdf();
        
        if ($destination === 'D') {
            // Download
            header('Content-Type: application/pdf');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Content-Length: ' . strlen($pdf_content));
            header('Cache-Control: no-cache, must-revalidate');
            header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
            echo $pdf_content;
            exit;
        } elseif ($destination === 'S') {
            // Return as string
            return $pdf_content;
        }
    }
    
    private function build_pdf() {
        $pdf = "%PDF-1.4\n";
        $objects = array();
        
        // Object 1: Catalog
        $objects[1] = "1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n";
        
        // Object 2: Pages
        $page_refs = array();
        for ($i = 1; $i <= count($this->pages); $i++) {
            $page_refs[] = (2 + $i) . " 0 R";
        }
        $objects[2] = "2 0 obj\n<<\n/Type /Pages\n/Kids [" . implode(' ', $page_refs) . "]\n/Count " . count($this->pages) . "\n>>\nendobj\n";
        
        // Font object
        $font_obj = 3 + count($this->pages);
        $objects[$font_obj] = "{$font_obj} 0 obj\n<<\n/Type /Font\n/Subtype /Type1\n/BaseFont /Helvetica\n>>\nendobj\n";
        
        // Page objects and content objects
        $obj_num = 3;
        foreach ($this->pages as $page_num => $page_data) {
            $content_obj = $obj_num + count($this->pages);
            
            // Page object
            $objects[$obj_num] = "{$obj_num} 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n/Contents {$content_obj} 0 R\n/Resources <<\n/Font <<\n/F1 {$font_obj} 0 R\n>>\n>>\n>>\nendobj\n";
            
            // Content object
            $content = $page_data['content'];
            $objects[$content_obj] = "{$content_obj} 0 obj\n<<\n/Length " . strlen($content) . "\n>>\nstream\n{$content}\nendstream\nendobj\n";
            
            $obj_num++;
        }
        
        // Build PDF
        $offset = strlen($pdf);
        $xref_offsets = array();
        
        foreach ($objects as $obj_num => $obj_content) {
            $xref_offsets[$obj_num] = $offset;
            $pdf .= $obj_content;
            $offset = strlen($pdf);
        }
        
        // Cross-reference table
        $xref_offset = $offset;
        $pdf .= "xref\n0 " . (count($objects) + 1) . "\n0000000000 65535 f \n";
        
        foreach ($xref_offsets as $obj_offset) {
            $pdf .= sprintf("%010d 00000 n \n", $obj_offset);
        }
        
        // Trailer
        $pdf .= "trailer\n<<\n/Size " . (count($objects) + 1) . "\n/Root 1 0 R\n>>\nstartxref\n{$xref_offset}\n%%EOF\n";
        
        return $pdf;
    }
}
