/**
 * Simple Invoice Admin JavaScript
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Global variables
    var SI_Admin = {
        init: function() {
            this.bindEvents();
            this.initMediaUploader();
            this.initTooltips();
        },

        bindEvents: function() {
            // Form submissions
            $(document).on('submit', '.si-form', this.handleFormSubmit);
            
            // Modal controls
            $(document).on('click', '.si-modal-trigger', this.openModal);
            $(document).on('click', '.si-modal-close', this.closeModal);
            $(document).on('click', '.si-modal', this.closeModalOnBackdrop);
            
            // AJAX actions
            $(document).on('click', '.si-ajax-action', this.handleAjaxAction);
            
            // Confirmation dialogs
            $(document).on('click', '.si-confirm', this.handleConfirmation);
            
            // Auto-save functionality
            $(document).on('input', '.si-auto-save', this.debounce(this.autoSave, 1000));
            
            // Search functionality
            $(document).on('input', '.si-search-input', this.debounce(this.handleSearch, 500));
            
            // Keyboard shortcuts
            $(document).on('keydown', this.handleKeyboardShortcuts);
        },

        initMediaUploader: function() {
            $(document).on('click', '.si-media-button', function(e) {
                e.preventDefault();
                
                var button = $(this);
                var targetInput = $('#' + button.data('target'));
                
                // Create media uploader
                var mediaUploader = wp.media({
                    title: si_admin.strings.select_image || 'Select Image',
                    button: {
                        text: si_admin.strings.use_image || 'Use this image'
                    },
                    multiple: false,
                    library: {
                        type: 'image'
                    }
                });
                
                // Handle selection
                mediaUploader.on('select', function() {
                    var attachment = mediaUploader.state().get('selection').first().toJSON();
                    targetInput.val(attachment.url);
                    
                    // Trigger change event
                    targetInput.trigger('change');
                    
                    // Update preview if exists
                    SI_Admin.updateImagePreview(targetInput, attachment.url);
                });
                
                mediaUploader.open();
            });
        },

        updateImagePreview: function(input, imageUrl) {
            var preview = input.siblings('.si-image-preview');
            
            if (preview.length === 0) {
                preview = $('<div class="si-image-preview"></div>');
                input.after(preview);
            }
            
            if (imageUrl) {
                preview.html('<img src="' + imageUrl + '" alt="Preview" style="max-width: 200px; height: auto; margin-top: 10px;" />');
            } else {
                preview.empty();
            }
        },

        initTooltips: function() {
            // Initialize tooltips for elements with title attributes
            $(document).on('mouseenter', '[title]', function() {
                var element = $(this);
                var title = element.attr('title');
                
                if (title && !element.data('tooltip-initialized')) {
                    element.data('tooltip-initialized', true);
                    
                    // Create tooltip
                    var tooltip = $('<div class="si-tooltip">' + title + '</div>');
                    $('body').append(tooltip);
                    
                    // Position tooltip
                    var offset = element.offset();
                    tooltip.css({
                        top: offset.top - tooltip.outerHeight() - 5,
                        left: offset.left + (element.outerWidth() / 2) - (tooltip.outerWidth() / 2)
                    });
                    
                    // Remove title to prevent default tooltip
                    element.removeAttr('title').data('original-title', title);
                    
                    // Show tooltip
                    tooltip.fadeIn(200);
                    
                    // Hide on mouse leave
                    element.on('mouseleave', function() {
                        tooltip.fadeOut(200, function() {
                            tooltip.remove();
                        });
                        element.attr('title', element.data('original-title'));
                        element.removeData('tooltip-initialized');
                    });
                }
            });
        },

        openModal: function(e) {
            e.preventDefault();
            
            var trigger = $(this);
            var modalId = trigger.data('modal') || trigger.attr('href');
            var modal = $(modalId);
            
            if (modal.length) {
                modal.show();
                modal.find('.si-modal-content').addClass('si-modal-animate-in');
                
                // Focus first input
                setTimeout(function() {
                    modal.find('input, textarea, select').first().focus();
                }, 100);
                
                // Prevent body scroll
                $('body').addClass('si-modal-open');
            }
        },

        closeModal: function(e) {
            if (e) {
                e.preventDefault();
            }
            
            var modal = $(this).closest('.si-modal');
            
            if (modal.length) {
                modal.find('.si-modal-content').removeClass('si-modal-animate-in');
                
                setTimeout(function() {
                    modal.hide();
                    $('body').removeClass('si-modal-open');
                }, 200);
            }
        },

        closeModalOnBackdrop: function(e) {
            if (e.target === this) {
                SI_Admin.closeModal.call(this, e);
            }
        },

        handleFormSubmit: function(e) {
            var form = $(this);
            var submitButton = form.find('[type="submit"]');
            
            // Validate form
            if (!SI_Admin.validateForm(form)) {
                e.preventDefault();
                return false;
            }
            
            // Show loading state
            submitButton.prop('disabled', true).addClass('si-loading');
            
            // If it's an AJAX form, handle it
            if (form.hasClass('si-ajax-form')) {
                e.preventDefault();
                SI_Admin.submitAjaxForm(form);
            }
        },

        validateForm: function(form) {
            var isValid = true;
            var firstError = null;
            
            // Clear previous errors
            form.find('.si-error').removeClass('si-error');
            form.find('.si-error-message').remove();
            
            // Validate required fields
            form.find('[required]').each(function() {
                var field = $(this);
                var value = field.val().trim();
                
                if (!value) {
                    SI_Admin.showFieldError(field, si_admin.strings.required_field || 'This field is required.');
                    isValid = false;
                    
                    if (!firstError) {
                        firstError = field;
                    }
                }
            });
            
            // Validate email fields
            form.find('input[type="email"]').each(function() {
                var field = $(this);
                var value = field.val().trim();
                
                if (value && !SI_Admin.isValidEmail(value)) {
                    SI_Admin.showFieldError(field, si_admin.strings.invalid_email || 'Please enter a valid email address.');
                    isValid = false;
                    
                    if (!firstError) {
                        firstError = field;
                    }
                }
            });
            
            // Focus first error field
            if (firstError) {
                firstError.focus();
            }
            
            return isValid;
        },

        showFieldError: function(field, message) {
            field.addClass('si-error');
            
            var errorElement = $('<div class="si-error-message">' + message + '</div>');
            field.after(errorElement);
        },

        isValidEmail: function(email) {
            var regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return regex.test(email);
        },

        submitAjaxForm: function(form) {
            var formData = form.serialize();
            var action = form.data('action');
            var nonce = form.find('[name*="nonce"]').val();
            
            $.ajax({
                url: si_admin.ajax_url,
                type: 'POST',
                data: formData + '&action=' + action + '&nonce=' + nonce,
                success: function(response) {
                    SI_Admin.handleAjaxResponse(response, form);
                },
                error: function() {
                    SI_Admin.showMessage(si_admin.strings.error_occurred || 'An error occurred. Please try again.', 'error');
                },
                complete: function() {
                    form.find('[type="submit"]').prop('disabled', false).removeClass('si-loading');
                }
            });
        },

        handleAjaxAction: function(e) {
            e.preventDefault();
            
            var element = $(this);
            var action = element.data('action');
            var nonce = element.data('nonce') || si_admin.nonce;
            var data = element.data();
            
            // Show loading state
            element.addClass('si-loading');
            
            // Prepare AJAX data
            var ajaxData = {
                action: action,
                nonce: nonce
            };
            
            // Add element data
            $.extend(ajaxData, data);
            
            $.ajax({
                url: si_admin.ajax_url,
                type: 'POST',
                data: ajaxData,
                success: function(response) {
                    SI_Admin.handleAjaxResponse(response, element);
                },
                error: function() {
                    SI_Admin.showMessage(si_admin.strings.error_occurred || 'An error occurred. Please try again.', 'error');
                },
                complete: function() {
                    element.removeClass('si-loading');
                }
            });
        },

        handleAjaxResponse: function(response, element) {
            if (response.success) {
                SI_Admin.showMessage(response.message || si_admin.strings.success, 'success');
                
                // Trigger custom event
                $(document).trigger('si_ajax_success', [response, element]);
            } else {
                SI_Admin.showMessage(response.message || si_admin.strings.error_occurred, 'error');
                
                // Trigger custom event
                $(document).trigger('si_ajax_error', [response, element]);
            }
        },

        showMessage: function(message, type) {
            type = type || 'info';
            
            var messageElement = $('<div class="si-message ' + type + '">' + message + '</div>');
            
            // Find message container or create one
            var container = $('.si-messages');
            if (container.length === 0) {
                container = $('<div class="si-messages"></div>');
                $('.wrap').prepend(container);
            }
            
            container.append(messageElement);
            
            // Auto-hide after 5 seconds
            setTimeout(function() {
                messageElement.fadeOut(function() {
                    messageElement.remove();
                });
            }, 5000);
        },

        handleConfirmation: function(e) {
            var element = $(this);
            var message = element.data('confirm') || si_admin.strings.confirm_delete || 'Are you sure?';
            
            if (!confirm(message)) {
                e.preventDefault();
                return false;
            }
        },

        autoSave: function() {
            var form = $(this).closest('form');
            
            if (form.hasClass('si-auto-save-enabled')) {
                var formData = form.serialize();
                
                $.ajax({
                    url: si_admin.ajax_url,
                    type: 'POST',
                    data: formData + '&action=si_auto_save',
                    success: function(response) {
                        if (response.success) {
                            SI_Admin.showAutoSaveIndicator();
                        }
                    }
                });
            }
        },

        showAutoSaveIndicator: function() {
            var indicator = $('.si-auto-save-indicator');
            
            if (indicator.length === 0) {
                indicator = $('<div class="si-auto-save-indicator">Saved</div>');
                $('body').append(indicator);
            }
            
            indicator.show().delay(2000).fadeOut();
        },

        handleSearch: function() {
            var searchInput = $(this);
            var searchTerm = searchInput.val();
            var targetTable = searchInput.data('target') || '.si-table';
            
            $(targetTable + ' tbody tr').each(function() {
                var row = $(this);
                var text = row.text().toLowerCase();
                
                if (text.indexOf(searchTerm.toLowerCase()) > -1) {
                    row.show();
                } else {
                    row.hide();
                }
            });
        },

        handleKeyboardShortcuts: function(e) {
            // Ctrl/Cmd + S for save
            if ((e.ctrlKey || e.metaKey) && e.which === 83) {
                e.preventDefault();
                $('.si-save-button, [type="submit"]').first().click();
            }
            
            // Escape to close modals
            if (e.which === 27) {
                $('.si-modal:visible').each(function() {
                    SI_Admin.closeModal.call(this);
                });
            }
        },

        debounce: function(func, wait) {
            var timeout;
            return function executedFunction() {
                var context = this;
                var args = arguments;
                
                var later = function() {
                    timeout = null;
                    func.apply(context, args);
                };
                
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // Utility functions
        formatCurrency: function(amount, currency) {
            currency = currency || '₹';
            return currency + parseFloat(amount).toFixed(2);
        },

        formatDate: function(date, format) {
            format = format || 'Y-m-d';
            var d = new Date(date);
            
            var year = d.getFullYear();
            var month = ('0' + (d.getMonth() + 1)).slice(-2);
            var day = ('0' + d.getDate()).slice(-2);
            
            return format.replace('Y', year).replace('m', month).replace('d', day);
        },

        generateId: function(prefix) {
            prefix = prefix || 'si';
            return prefix + '_' + Math.random().toString(36).substr(2, 9);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        SI_Admin.init();
    });

    // Make SI_Admin globally available
    window.SI_Admin = SI_Admin;

})(jQuery);
