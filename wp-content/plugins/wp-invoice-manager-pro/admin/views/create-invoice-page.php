<?php
/**
 * Create Invoice Page Template
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get clients and templates
$client_manager = new SI_Client();
$template_manager = new SI_Template();
$clients = $client_manager->si_get_clients();
$templates = $template_manager->si_get_templates();

// Get selected template if provided
$selected_template_id = isset($_GET['template_id']) ? intval($_GET['template_id']) : 0;
$selected_template = null;

if ($selected_template_id > 0) {
    $selected_template = $template_manager->si_get_template($selected_template_id);
}
?>

<div class="wrap">
    <h1 class="wp-heading-inline"><?php echo esc_html__('Create Invoice', 'simple-invoice'); ?></h1>
    <a href="<?php echo esc_url(admin_url('admin.php?page=si-invoices')); ?>" class="page-title-action"><?php echo esc_html__('View All Invoices', 'simple-invoice'); ?></a>

    <hr class="wp-header-end">
    
    <?php if (empty($templates)): ?>
        <div class="notice notice-warning">
            <p>
                <strong><?php echo esc_html__('No Templates Found!', 'simple-invoice'); ?></strong><br>
                <?php echo esc_html__('You need to create at least one template before creating invoices.', 'simple-invoice'); ?>
            </p>
            <p>
                <a href="<?php echo esc_url(admin_url('admin.php?page=si-templates')); ?>" class="button button-primary">
                    <?php echo esc_html__('Create Your First Template', 'simple-invoice'); ?>
                </a>
            </p>
        </div>
    <?php elseif (empty($clients)): ?>
        <div class="notice notice-warning">
            <p>
                <strong><?php echo esc_html__('No Clients Found!', 'simple-invoice'); ?></strong><br>
                <?php echo esc_html__('You need to add at least one client before creating invoices.', 'simple-invoice'); ?>
            </p>
            <p>
                <a href="<?php echo esc_url(admin_url('admin.php?page=si-clients')); ?>" class="button button-primary">
                    <?php echo esc_html__('Add Your First Client', 'simple-invoice'); ?>
                </a>
            </p>
        </div>
    <?php else: ?>
        
        <form id="si-invoice-form" class="si-invoice-form">
            
            <!-- Invoice Header -->
            <div class="si-form-section">
                <h2><?php echo esc_html__('Invoice Details', 'simple-invoice'); ?></h2>
                
                <div class="si-form-row">
                    <div class="si-form-col">
                        <label for="si-template-select"><?php echo esc_html__('Template', 'simple-invoice'); ?> <span class="required">*</span></label>
                        <select id="si-template-select" name="template_id" required>
                            <option value=""><?php echo esc_html__('Select Template', 'simple-invoice'); ?></option>
                            <?php foreach ($templates as $template): ?>
                                <option value="<?php echo esc_attr($template->id); ?>" 
                                        <?php selected($template->id, $selected_template_id); ?>>
                                    <?php echo esc_html($template->name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="si-form-col">
                        <label for="si-client-select"><?php echo esc_html__('Client', 'simple-invoice'); ?> <span class="required">*</span></label>
                        <div class="si-client-select-wrapper">
                            <select id="si-client-select" name="client_id" required>
                                <option value=""><?php echo esc_html__('Select Client', 'simple-invoice'); ?></option>
                                <?php foreach ($clients as $client): ?>
                                    <option value="<?php echo esc_attr($client->id); ?>">
                                        <?php echo esc_html($client->name); ?>
                                        <?php if (!empty($client->business_name)): ?>
                                            (<?php echo esc_html($client->business_name); ?>)
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <button type="button" class="button si-add-client-quick" title="<?php echo esc_attr__('Add New Client', 'simple-invoice'); ?>">
                                <span class="dashicons dashicons-plus-alt"></span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="si-form-row">
                    <div class="si-form-col">
                        <label for="si-invoice-number"><?php echo esc_html__('Invoice Number', 'simple-invoice'); ?></label>
                        <input type="text" 
                               id="si-invoice-number" 
                               name="invoice_number" 
                               class="regular-text" 
                               placeholder="<?php echo esc_attr__('Auto-generated if empty', 'simple-invoice'); ?>" />
                    </div>
                    
                    <div class="si-form-col">
                        <label for="si-invoice-date"><?php echo esc_html__('Invoice Date', 'simple-invoice'); ?></label>
                        <input type="date" 
                               id="si-invoice-date" 
                               name="invoice_date" 
                               value="<?php echo esc_attr(date('Y-m-d')); ?>" 
                               class="regular-text" />
                    </div>
                </div>
            </div>
            
            <!-- Invoice Items -->
            <div class="si-form-section">
                <h2><?php echo esc_html__('Invoice Items', 'simple-invoice'); ?></h2>

                <div class="si-invoice-items-wrapper">
                    <table class="si-invoice-items-table" id="si-invoice-items">
                        <thead>
                            <tr id="si-items-header">
                                <th><?php echo esc_html__('Sr No.', 'simple-invoice'); ?></th>
                                <th><?php echo esc_html__('Description', 'simple-invoice'); ?></th>
                                <th><?php echo esc_html__('Quantity', 'simple-invoice'); ?></th>
                                <th><?php echo esc_html__('Rate', 'simple-invoice'); ?></th>
                                <th><?php echo esc_html__('Total', 'simple-invoice'); ?></th>
                                <th><?php echo esc_html__('Actions', 'simple-invoice'); ?></th>
                            </tr>
                        </thead>
                        <tbody id="si-items-body">
                            <!-- Items will be added here -->
                        </tbody>
                    </table>

                    <button type="button" class="button" id="si-add-item">
                        <span class="dashicons dashicons-plus-alt"></span>
                        <?php echo esc_html__('Add Item', 'simple-invoice'); ?>
                    </button>
                </div>
            </div>
            
            <!-- Invoice Summary -->
            <div class="si-form-section">
                <h2><?php echo esc_html__('Invoice Summary', 'simple-invoice'); ?></h2>

                <div class="si-invoice-summary" id="si-invoice-summary">
                    <div class="si-summary-grid">
                        <div class="si-summary-row">
                            <label><?php echo esc_html__('Subtotal:', 'simple-invoice'); ?></label>
                            <span class="si-summary-value" id="si-subtotal">0.00</span>
                        </div>
                        <div class="si-summary-row">
                            <label><?php echo esc_html__('Tax (%):', 'simple-invoice'); ?></label>
                            <input type="number" class="si-summary-input" id="si-tax-rate" name="tax_rate" step="0.01" min="0" value="0" />
                        </div>
                        <div class="si-summary-row">
                            <label><?php echo esc_html__('Tax Amount:', 'simple-invoice'); ?></label>
                            <span class="si-summary-value" id="si-tax-amount">0.00</span>
                        </div>
                        <div class="si-summary-row">
                            <label><?php echo esc_html__('Discount:', 'simple-invoice'); ?></label>
                            <input type="number" class="si-summary-input" id="si-discount" name="discount" step="0.01" min="0" value="0" />
                        </div>
                        <div class="si-summary-row">
                            <label><?php echo esc_html__('Shipping:', 'simple-invoice'); ?></label>
                            <input type="number" class="si-summary-input" id="si-shipping" name="shipping" step="0.01" min="0" value="0" />
                        </div>
                        <div class="si-summary-row si-total-row">
                            <label><?php echo esc_html__('Total Amount:', 'simple-invoice'); ?></label>
                            <span class="si-summary-value si-total-amount" id="si-total-amount">0.00</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Additional Notes -->
            <div class="si-form-section">
                <h2><?php echo esc_html__('Additional Notes', 'simple-invoice'); ?></h2>
                
                <textarea id="si-invoice-notes" 
                          name="notes" 
                          rows="4" 
                          class="large-text" 
                          placeholder="<?php echo esc_attr__('Any additional notes or instructions for the client...', 'simple-invoice'); ?>"></textarea>
            </div>
            
            <!-- Form Actions -->
            <div class="si-form-actions">
                <button type="button" class="button button-large" id="si-preview-invoice">
                    <?php echo esc_html__('Preview Invoice', 'simple-invoice'); ?>
                </button>
                <button type="button" class="button button-primary button-large" id="si-create-invoice">
                    <?php echo esc_html__('Create & Download PDF', 'simple-invoice'); ?>
                </button>
            </div>
            
        </form>
        
    <?php endif; ?>
</div>

<!-- Quick Add Client Modal -->
<div id="si-quick-client-modal" class="si-modal" style="display: none;">
    <div class="si-modal-content">
        <div class="si-modal-header">
            <h2><?php echo esc_html__('Add New Client', 'simple-invoice'); ?></h2>
            <button type="button" class="si-modal-close">&times;</button>
        </div>
        
        <div class="si-modal-body">
            <form id="si-quick-client-form">
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="si-quick-client-name"><?php echo esc_html__('Name', 'simple-invoice'); ?> <span class="required">*</span></label>
                        </th>
                        <td>
                            <input type="text" 
                                   id="si-quick-client-name" 
                                   name="name" 
                                   class="regular-text" 
                                   required 
                                   placeholder="<?php echo esc_attr__('Client full name', 'simple-invoice'); ?>" />
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="si-quick-client-business"><?php echo esc_html__('Business Name', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="text" 
                                   id="si-quick-client-business" 
                                   name="business_name" 
                                   class="regular-text" 
                                   placeholder="<?php echo esc_attr__('Business or company name', 'simple-invoice'); ?>" />
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="si-quick-client-email"><?php echo esc_html__('Email', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="email" 
                                   id="si-quick-client-email" 
                                   name="email" 
                                   class="regular-text" 
                                   placeholder="<?php echo esc_attr__('<EMAIL>', 'simple-invoice'); ?>" />
                        </td>
                    </tr>
                </table>
            </form>
        </div>
        
        <div class="si-modal-footer">
            <button type="button" class="button button-secondary si-modal-close"><?php echo esc_html__('Cancel', 'simple-invoice'); ?></button>
            <button type="button" class="button button-primary" id="si-save-quick-client"><?php echo esc_html__('Add Client', 'simple-invoice'); ?></button>
        </div>
    </div>
</div>

<!-- Invoice Preview Modal -->
<div id="si-preview-modal" class="si-modal si-modal-large" style="display: none;">
    <div class="si-modal-content">
        <div class="si-modal-header">
            <h2><?php echo esc_html__('Invoice Preview', 'simple-invoice'); ?></h2>
            <button type="button" class="si-modal-close">&times;</button>
        </div>
        
        <div class="si-modal-body">
            <div id="si-preview-content">
                <!-- Preview content will be loaded here -->
            </div>
        </div>
        
        <div class="si-modal-footer">
            <button type="button" class="button button-secondary si-modal-close"><?php echo esc_html__('Close', 'simple-invoice'); ?></button>
            <button type="button" class="button button-primary" id="si-create-from-preview">
                <?php echo esc_html__('Create & Download PDF', 'simple-invoice'); ?>
            </button>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    var itemCounter = 0;

    // Add invoice item
    $('#si-add-item').on('click', function() {
        addInvoiceItem();
    });

    // Remove invoice item
    $(document).on('click', '.si-remove-item', function() {
        $(this).closest('tr').remove();
        updateSerialNumbers();
        calculateTotals();
    });

    // Calculate totals on input change
    $(document).on('input', '.si-item-quantity, .si-item-rate, .si-summary-input', function() {
        calculateTotals();
    });

    // Quick add client
    $('.si-add-client-quick').on('click', function() {
        $('#si-quick-client-modal').show();
    });

    // Save quick client
    $('#si-save-quick-client').on('click', function() {
        saveQuickClient();
    });

    // Preview invoice
    $('#si-preview-invoice').on('click', function() {
        if (validateForm()) {
            previewInvoice();
        }
    });

    // Create invoice
    $('#si-create-invoice, #si-create-from-preview').on('click', function() {
        if (validateForm()) {
            createInvoice();
        }
    });

    // Close modals
    $('.si-modal-close').on('click', function() {
        $(this).closest('.si-modal').hide();
    });

    function addInvoiceItem() {
        itemCounter++;
        var rowHtml = '<tr data-item-index="' + itemCounter + '">';

        // Serial number
        rowHtml += '<td><span class="si-item-serial">' + itemCounter + '</span></td>';

        // Description
        rowHtml += '<td><input type="text" class="si-item-description" name="items[' + itemCounter + '][description]" placeholder="<?php echo esc_attr__('Item description', 'simple-invoice'); ?>" required /></td>';

        // Quantity
        rowHtml += '<td><input type="number" class="si-item-quantity" name="items[' + itemCounter + '][quantity]" step="1" min="1" value="1" required /></td>';

        // Rate
        rowHtml += '<td><input type="number" class="si-item-rate" name="items[' + itemCounter + '][rate]" step="0.01" min="0" placeholder="0.00" required /></td>';

        // Total (calculated)
        rowHtml += '<td><span class="si-item-total">0.00</span></td>';

        // Actions
        rowHtml += '<td><button type="button" class="button button-small si-remove-item"><?php echo esc_js(__('Remove', 'simple-invoice')); ?></button></td>';

        rowHtml += '</tr>';

        $('#si-items-body').append(rowHtml);
        calculateTotals();
    }

    function updateSerialNumbers() {
        $('#si-items-body tr').each(function(index) {
            $(this).find('.si-item-serial').text(index + 1);
            $(this).attr('data-item-index', index + 1);
        });
        itemCounter = $('#si-items-body tr').length;
    }

    function validateForm() {
        var isValid = true;
        var errors = [];

        // Check if client is selected
        if (!$('#si-client-select').val()) {
            errors.push('<?php echo esc_js(__('Please select a client.', 'simple-invoice')); ?>');
            isValid = false;
        }

        // Check if template is selected
        if (!$('#si-template-select').val()) {
            errors.push('<?php echo esc_js(__('Please select a template.', 'simple-invoice')); ?>');
            isValid = false;
        }

        // Check if there are items
        if ($('#si-items-body tr').length === 0) {
            errors.push('<?php echo esc_js(__('Please add at least one item.', 'simple-invoice')); ?>');
            isValid = false;
        }

        // Validate item fields
        var hasEmptyItems = false;
        $('#si-items-body tr').each(function() {
            var description = $(this).find('.si-item-description').val().trim();
            var quantity = $(this).find('.si-item-quantity').val();
            var rate = $(this).find('.si-item-rate').val();

            if (!description || !quantity || !rate) {
                hasEmptyItems = true;
            }
        });

        if (hasEmptyItems) {
            errors.push('<?php echo esc_js(__('Please fill in all item fields.', 'simple-invoice')); ?>');
            isValid = false;
        }

        if (!isValid) {
            alert(errors.join('\\n'));
        }

        return isValid;
    }
    
    function calculateTotals() {
        var subtotal = 0;

        // Calculate item totals
        $('#si-items-body tr').each(function() {
            var row = $(this);
            var quantity = parseFloat(row.find('.si-item-quantity').val()) || 0;
            var rate = parseFloat(row.find('.si-item-rate').val()) || 0;
            var total = quantity * rate;

            row.find('.si-item-total').text(total.toFixed(2));
            subtotal += total;
        });

        // Update subtotal
        $('#si-subtotal').text(subtotal.toFixed(2));

        // Get tax, discount, and shipping values
        var taxRate = parseFloat($('#si-tax-rate').val()) || 0;
        var discount = parseFloat($('#si-discount').val()) || 0;
        var shipping = parseFloat($('#si-shipping').val()) || 0;

        // Calculate tax amount
        var taxAmount = subtotal * (taxRate / 100);
        $('#si-tax-amount').text(taxAmount.toFixed(2));

        // Calculate final total
        var finalTotal = subtotal + taxAmount - discount + shipping;
        $('#si-total-amount').text(finalTotal.toFixed(2));
    }
    
    function saveQuickClient() {
        var formData = $('#si-quick-client-form').serialize();
        formData += '&action=si_add_client&nonce=<?php echo wp_create_nonce('si_add_client_nonce'); ?>';
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success && response.data.client) {
                    var client = response.data.client;
                    var optionText = client.name;
                    if (client.business_name) {
                        optionText += ' (' + client.business_name + ')';
                    }
                    
                    $('#si-client-select').append('<option value="' + client.id + '">' + optionText + '</option>');
                    $('#si-client-select').val(client.id);
                    $('#si-quick-client-modal').hide();
                    $('#si-quick-client-form')[0].reset();
                } else {
                    alert(response.message || '<?php echo esc_js(__('Failed to add client.', 'simple-invoice')); ?>');
                }
            }
        });
    }
    
    function previewInvoice() {
        var formData = collectInvoiceData();

        // Show loading state
        $('#si-preview-invoice').prop('disabled', true).text('<?php echo esc_js(__('Loading...', 'simple-invoice')); ?>');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_preview_invoice',
                client_id: formData.client_id,
                template_id: formData.template_id,
                invoice_data: formData.invoice_data,
                nonce: '<?php echo wp_create_nonce('si_preview_invoice_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    $('#si-preview-content').html(response.data.html_content);
                    $('#si-preview-modal').show();
                } else {
                    alert(response.data || '<?php echo esc_js(__('Failed to generate preview.', 'simple-invoice')); ?>');
                }
            },
            error: function(xhr, status, error) {
                alert('<?php echo esc_js(__('An error occurred while generating preview.', 'simple-invoice')); ?>');
            },
            complete: function() {
                // Reset button state
                $('#si-preview-invoice').prop('disabled', false).text('<?php echo esc_js(__('Preview Invoice', 'simple-invoice')); ?>');
            }
        });
    }
    
    function createInvoice() {
        var formData = collectInvoiceData();

        // Show loading state
        $('#si-create-invoice, #si-create-from-preview').prop('disabled', true).text('<?php echo esc_js(__('Creating...', 'simple-invoice')); ?>');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_create_invoice',
                client_id: formData.client_id,
                template_id: formData.template_id,
                invoice_number: formData.invoice_number,
                invoice_data: formData.invoice_data,
                nonce: '<?php echo wp_create_nonce('si_create_invoice_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    alert(response.data.message || '<?php echo esc_js(__('Invoice created successfully!', 'simple-invoice')); ?>');

                    // Open download URL in new window/tab
                    window.open(response.data.download_url, '_blank');

                    // Reset form after successful creation
                    $('#si-invoice-form')[0].reset();
                    $('#si-items-body').empty();
                    addInvoiceItem(); // Add one default item
                    calculateTotals();
                } else {
                    alert(response.data || '<?php echo esc_js(__('Failed to create invoice.', 'simple-invoice')); ?>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                alert('<?php echo esc_js(__('An error occurred. Please try again.', 'simple-invoice')); ?>');
            },
            complete: function() {
                // Reset button state
                $('#si-create-invoice, #si-create-from-preview').prop('disabled', false).text('<?php echo esc_js(__('Create & Download PDF', 'simple-invoice')); ?>');
            }
        });
    }
    
    function collectInvoiceData() {
        var items = [];

        // Collect items
        $('#si-items-body tr').each(function() {
            var row = $(this);
            var item = {
                description: row.find('.si-item-description').val(),
                quantity: parseFloat(row.find('.si-item-quantity').val()) || 0,
                rate: parseFloat(row.find('.si-item-rate').val()) || 0
            };

            if (item.description && item.quantity > 0 && item.rate >= 0) {
                items.push(item);
            }
        });

        var formData = {
            client_id: $('#si-client-select').val(),
            template_id: $('#si-template-select').val(),
            invoice_number: $('#si-invoice-number').val(),
            invoice_data: {
                items: items,
                tax_rate: parseFloat($('#si-tax-rate').val()) || 0,
                discount: parseFloat($('#si-discount').val()) || 0,
                shipping: parseFloat($('#si-shipping').val()) || 0,
                notes: $('#si-invoice-notes').val()
            }
        };

        return formData;
    }

    // Add first item on page load
    $(document).ready(function() {
        addInvoiceItem();
    });
});
</script>
