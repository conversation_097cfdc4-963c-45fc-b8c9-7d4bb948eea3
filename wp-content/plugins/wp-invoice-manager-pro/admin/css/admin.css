/**
 * Simple Invoice Admin Styles
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

/* Admin Menu Styles */
#adminmenu .toplevel_page_simple-invoice .wp-menu-image:before {
    content: '\f123';
    font-family: dashicons;
}

#adminmenu .toplevel_page_simple-invoice .wp-submenu a {
    position: relative;
}

#adminmenu .toplevel_page_simple-invoice .wp-submenu .dashicons {
    font-size: 16px !important;
    width: 16px !important;
    height: 16px !important;
    margin-right: 5px !important;
    vertical-align: middle;
    color: rgba(255, 255, 255, 0.6);
}

#adminmenu .toplevel_page_simple-invoice .wp-submenu .current .dashicons,
#adminmenu .toplevel_page_simple-invoice .wp-submenu a:hover .dashicons {
    color: #f47a45;
}

/* Menu item spacing */
#adminmenu .toplevel_page_simple-invoice .wp-submenu li {
    margin: 0;
}

#adminmenu .toplevel_page_simple-invoice .wp-submenu a {
    padding: 5px 12px;
    display: flex;
    align-items: center;
}

/* General Styles */
.si-settings-container {
    max-width: 1000px;
}

.si-settings-section {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    margin-bottom: 20px;
    padding: 0;
}

.si-settings-section h2 {
    background: #e7e7e7;
    border-bottom: 1px solid #e7e7e7;
    margin: 0;
    padding: 15px 20px;
    font-size: 16px;
    font-weight: 600;
}

.si-settings-section .form-table {
    margin: 0;
    padding: 20px;
}

/* Media Upload */
.si-media-button {
    margin-left: 10px;
    vertical-align: top;
}

.si-logo-preview {
    margin-top: 10px;
}

.si-logo-preview img {
    max-width: 200px;
    height: auto;
    border: 1px solid #e7e7e7;
    padding: 5px;
    background: #ffffff;
}

/* Modal Styles */
.si-modal {
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
}

.si-modal-content {
    background-color: #ffffff;
    border-radius: 4px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

.si-modal-large .si-modal-content {
    max-width: 900px;
}

.si-modal-header {
    background: #e7e7e7;
    border-bottom: 1px solid #e7e7e7;
    padding: 15px 20px;
    position: relative;
}

.si-modal-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.si-modal-close {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #5f5f5f;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.si-modal-close:hover {
    color: #000000;
}

.si-modal-body {
    padding: 20px;
}

.si-modal-footer {
    background: #e7e7e7;
    border-top: 1px solid #e7e7e7;
    padding: 15px 20px;
    text-align: right;
}

.si-modal-footer .button {
    margin-left: 10px;
}

/* Clients Page */
.si-clients-filters {
    margin: 20px 0;
    padding: 15px;
    background: #e7e7e7;
    border: 1px solid #e7e7e7;
    border-radius: 4px;
}

.si-search-box {
    display: flex;
    align-items: center;
    gap: 10px;
}

.si-search-box input {
    flex: 1;
    max-width: 300px;
}

.si-clients-table-wrapper {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.si-empty-field {
    color: #5f5f5f;
    font-style: italic;
}

.si-no-clients {
    text-align: center;
    padding: 40px 20px;
    color: #5f5f5f;
}

.si-actions-column {
    white-space: nowrap;
}

.si-actions-column .button {
    margin-right: 5px;
}

/* Templates Page */
.si-templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.si-template-card {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease;
}

.si-template-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.si-template-preview {
    height: 200px;
    background: #e7e7e7;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.si-template-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.si-template-placeholder {
    text-align: center;
    color: #5f5f5f;
}

.si-template-placeholder .dashicons {
    font-size: 48px;
    margin-bottom: 10px;
}

.si-template-info {
    padding: 15px;
}

.si-template-info h3 {
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: 600;
}

.si-template-info p {
    margin: 5px 0;
    font-size: 13px;
    color: #5f5f5f;
}

.si-template-actions {
    padding: 15px;
    border-top: 1px solid #e7e7e7;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.si-template-actions .button {
    flex: 1;
    text-align: center;
    min-width: 80px;
}

.si-no-templates {
    text-align: center;
    padding: 60px 20px;
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
}

.si-no-templates-icon .dashicons {
    font-size: 64px;
    color: #e7e7e7;
    margin-bottom: 20px;
}

.si-no-templates h3 {
    margin: 0 0 10px 0;
    color: #5f5f5f;
}

.si-no-templates p {
    margin: 0 0 20px 0;
    color: #5f5f5f;
}

/* Template Form */
.si-template-form-sections {
    max-height: 70vh;
    overflow-y: auto;
}

.si-form-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e7e7e7;
}

.si-form-section:last-child {
    border-bottom: none;
}

.si-form-section h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    color: #000000;
}

.si-design-selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.si-design-option {
    cursor: pointer;
    display: block;
}

.si-design-option input[type="radio"] {
    display: none;
}

.si-design-preview {
    border: 2px solid #e7e7e7;
    border-radius: 8px;
    padding: 10px;
    text-align: center;
    transition: border-color 0.3s ease;
}

.si-design-option input[type="radio"]:checked + .si-design-preview {
    border-color: #f47a45;
    background: #ffffff;
}

.si-design-preview img {
    width: 100%;
    height: 100px;
    object-fit: cover;
    border-radius: 4px;
    margin-bottom: 8px;
}

.si-design-placeholder {
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #e7e7e7;
    border-radius: 4px;
    margin-bottom: 8px;
}

.si-design-placeholder .dashicons {
    font-size: 32px;
    color: #5f5f5f;
}

.si-design-name {
    font-size: 13px;
    font-weight: 600;
    color: #000000;
}

.si-checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.si-checkbox-grid label {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    background: #e7e7e7;
    border-radius: 4px;
    cursor: pointer;
}

.si-checkbox-grid label:hover {
    background: #ffffff;
}

.si-field-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    padding: 10px;
    background: #e7e7e7;
    border-radius: 4px;
}

.si-field-row input,
.si-field-row select {
    flex: 1;
}

.si-field-row label {
    white-space: nowrap;
}

/* Create Invoice Page */
.si-invoice-form {
    max-width: 1200px;
}

.si-form-section {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    margin-bottom: 20px;
    padding: 20px;
    border-radius: 4px;
}

.si-form-section h2 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #000000;
    border-bottom: 1px solid #e7e7e7;
    padding-bottom: 10px;
}

.si-form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.si-form-col {
    display: flex;
    flex-direction: column;
}

.si-form-col label {
    font-weight: 600;
    margin-bottom: 8px;
    color: #000000;
}

.si-form-col input,
.si-form-col select {
    padding: 8px 12px;
    border: 1px solid #e7e7e7;
    border-radius: 4px;
    font-size: 14px;
}

.si-client-select-wrapper {
    display: flex;
    gap: 8px;
    align-items: flex-end;
}

.si-client-select-wrapper select {
    flex: 1;
}

.si-add-client-quick {
    padding: 8px 12px;
    min-width: auto;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.si-invoice-items-wrapper {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 4px;
    padding: 0;
    overflow: hidden;
}

.si-invoice-items-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.si-invoice-items-table th {
    background: #e7e7e7;
    color: #000000;
    font-weight: 600;
    padding: 15px 12px;
    text-align: left;
    border-bottom: 2px solid #e7e7e7;
    font-size: 13px;
}

.si-invoice-items-table td {
    padding: 12px;
    border-bottom: 1px solid #e7e7e7;
    vertical-align: middle;
}

.si-invoice-items-table tbody tr:hover {
    background: #e7e7e7;
}

.si-invoice-items-table input {
    width: 100%;
    border: 1px solid #e7e7e7;
    padding: 8px 10px;
    border-radius: 3px;
    font-size: 14px;
}

.si-invoice-items-table input:focus {
    border-color: #f47a45;
    box-shadow: 0 0 0 1px #f47a45;
    outline: none;
}

.si-item-total {
    font-weight: 600;
    color: #f47a45;
    font-size: 14px;
}

.si-invoice-items-table .si-remove-item {
    background: #5f5f5f;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
}

.si-invoice-items-table .si-remove-item:hover {
    background: #000000;
}

#si-add-item {
    margin: 15px;
    background: #f47a45;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
}

#si-add-item:hover {
    background: #5f5f5f;
}

.si-invoice-summary {
    background: #e7e7e7;
    border: 1px solid #e7e7e7;
    border-radius: 4px;
    padding: 20px;
}

.si-summary-grid {
    display: grid;
    grid-template-columns: 1fr 150px;
    gap: 12px;
    align-items: center;
    max-width: 400px;
    margin-left: auto;
}

.si-summary-row {
    display: contents;
}

.si-summary-row label {
    font-weight: 600;
    text-align: right;
    color: #000000;
}

.si-summary-value {
    text-align: right;
    font-weight: 600;
    padding: 8px 12px;
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 3px;
    color: #000000;
}

.si-total-row label,
.si-total-row .si-summary-value {
    font-size: 16px;
    color: #f47a45;
    border-top: 2px solid #f47a45;
    padding-top: 12px;
    margin-top: 8px;
}

.si-summary-input {
    text-align: right;
    padding: 8px 12px;
    border: 1px solid #e7e7e7;
    border-radius: 3px;
    font-size: 14px;
}

.si-summary-input:focus {
    border-color: #f47a45;
    box-shadow: 0 0 0 1px #f47a45;
    outline: none;
}

.si-form-actions {
    margin-top: 30px;
    text-align: center;
    padding: 25px;
    background: #e7e7e7;
    border-radius: 4px;
    border: 1px solid #e7e7e7;
}

.si-form-actions .button {
    margin: 0 10px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
}

.si-form-actions .button-primary {
    background: #f47a45;
    border-color: #f47a45;
}

.si-form-actions .button-primary:hover {
    background: #5f5f5f;
    border-color: #5f5f5f;
}

/* Required field indicator */
.required {
    color: #f47a45;
}

/* Additional Invoice Form Styles */
.si-item-serial {
    font-weight: 600;
    color: #5f5f5f;
    text-align: center;
    display: block;
}

.si-invoice-items-table th:first-child,
.si-invoice-items-table td:first-child {
    text-align: center;
    width: 60px;
}

.si-invoice-items-table th:nth-child(3),
.si-invoice-items-table td:nth-child(3) {
    width: 100px;
}

.si-invoice-items-table th:nth-child(4),
.si-invoice-items-table td:nth-child(4) {
    width: 120px;
}

.si-invoice-items-table th:nth-child(5),
.si-invoice-items-table td:nth-child(5) {
    width: 120px;
    text-align: right;
}

.si-invoice-items-table th:last-child,
.si-invoice-items-table td:last-child {
    width: 100px;
    text-align: center;
}

.si-invoice-items-table .si-item-total {
    text-align: right;
    display: block;
}

/* Empty state */
.si-no-items {
    text-align: center;
    padding: 40px 20px;
    color: #5f5f5f;
    font-style: italic;
}

/* Form validation */
.si-error {
    border-color: #f47a45 !important;
    box-shadow: 0 0 0 1px #f47a45 !important;
}

.si-error-message {
    color: #f47a45;
    font-size: 12px;
    margin-top: 4px;
    display: block;
}

/* Templates Page - Consistent with Dashboard Style */
.si-templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

/* Design Management Styles */
.si-design-management {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.si-designs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.si-design-card {
    background: #ffffff;
    border: 2px solid #e7e7e7;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.si-design-card:hover {
    border-color: #f47a45;
    box-shadow: 0 4px 12px rgba(244,122,69,0.15);
    transform: translateY(-2px);
}

.si-design-card.si-add-design-card {
    border-style: dashed;
    border-color: #e7e7e7;
}

.si-design-card.si-add-design-card:hover {
    border-color: #f47a45;
    background: #e7e7e7;
}

.si-design-preview {
    height: 180px;
    background: #e7e7e7;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.si-design-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.si-design-placeholder {
    text-align: center;
    color: #5f5f5f;
}

.si-design-placeholder .dashicons {
    font-size: 48px;
    margin-bottom: 10px;
    opacity: 0.6;
}

.si-design-placeholder .si-design-name {
    display: block;
    font-size: 14px;
    font-weight: 500;
}

.si-design-info {
    padding: 15px;
}

.si-design-title {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #000000;
}

.si-design-description {
    margin: 0 0 10px 0;
    font-size: 13px;
    color: #5f5f5f;
    line-height: 1.4;
}

.si-design-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
}

.si-design-id {
    font-size: 11px;
    color: #5f5f5f;
    background: #e7e7e7;
    padding: 2px 6px;
    border-radius: 3px;
}

.si-design-actions {
    margin-top: 10px;
}

/* Design Guide Modal Styles */
.si-guide-content {
    max-width: 100%;
}

.si-guide-step {
    margin-bottom: 25px;
    padding: 20px;
    background: #e7e7e7;
    border-radius: 6px;
    border-left: 4px solid #f47a45;
}

.si-guide-step h4 {
    margin: 0 0 10px 0;
    color: #f47a45;
    display: flex;
    align-items: center;
    gap: 10px;
}

.si-step-number {
    background: #f47a45;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
}

.si-guide-step code {
    background: #e7e7e7;
    padding: 8px 12px;
    border-radius: 4px;
    display: block;
    margin: 8px 0;
    font-family: 'Courier New', monospace;
    color: #000000;
}

.si-variables-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.si-variable-group {
    background: white;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #e7e7e7;
}

.si-variable-group h5 {
    margin: 0 0 10px 0;
    color: #000000;
    font-size: 14px;
}

.si-variable-group ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.si-variable-group li {
    margin-bottom: 5px;
}

.si-variable-group code {
    background: #e7e7e7;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    color: #f47a45;
    display: inline;
    margin: 0;
}

.si-guide-actions {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #e7e7e7;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.si-guide-step ol {
    margin: 10px 0 0 20px;
}

.si-guide-step ul {
    margin: 10px 0 0 20px;
}

.si-guide-step li {
    margin-bottom: 5px;
    line-height: 1.4;
}

/* Section Headers */
.si-section-header {
    margin-bottom: 20px;
}

.si-section-title {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 600;
    color: #000000;
    display: flex;
    align-items: center;
    gap: 10px;
}

.si-section-title .dashicons {
    font-size: 24px;
    width: 24px;
    height: 24px;
    color: #f47a45;
}

.si-section-description {
    margin: 0;
    color: #5f5f5f;
    font-size: 14px;
    line-height: 1.5;
}

/* Designs Link Section */
.si-designs-link-section {
    max-width: 1200px;
    margin: 0 auto 30px auto;
    padding: 0 20px;
}

.si-info-card {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: white;
    border-radius: 12px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    box-shadow: 0 4px 12px rgba(244, 122, 69, 0.3);
}

.si-info-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.si-info-icon .dashicons {
    font-size: 28px;
    width: 28px;
    height: 28px;
    color: white;
}

.si-info-content {
    flex: 1;
}

.si-info-content h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
}

.si-info-content p {
    margin: 0 0 15px 0;
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    line-height: 1.5;
}

.si-info-content .si-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.si-info-content .si-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    transform: translateY(-1px);
}

.si-template-card {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    transition: all 0.2s ease;
}

.si-template-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,.1);
    transform: translateY(-1px);
}

.si-template-preview {
    margin-bottom: 15px;
    text-align: center;
}

.si-template-preview img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    border: 1px solid #e7e7e7;
}

.si-template-placeholder {
    background: #e7e7e7;
    border: 1px solid #e7e7e7;
    border-radius: 4px;
    padding: 40px 20px;
    text-align: center;
    color: #5f5f5f;
}

.si-template-placeholder .dashicons {
    font-size: 48px;
    margin-bottom: 10px;
    color: #5f5f5f;
}

.si-template-info h3 {
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: 600;
    color: #000000;
}

.si-template-design,
.si-template-date {
    margin: 5px 0;
    font-size: 13px;
    color: #5f5f5f;
}

.si-template-actions {
    margin-top: 15px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.si-template-actions .button {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 13px;
}

.si-template-actions .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Empty State - Consistent Style */
.si-empty-state {
    text-align: center;
    padding: 60px 40px;
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    margin: 20px 0;
}

.si-empty-icon {
    margin-bottom: 20px;
}

.si-empty-icon .dashicons {
    font-size: 64px;
    width: 64px;
    height: 64px;
    color: #5f5f5f;
}

.si-empty-state h3 {
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
    color: #000000;
}

.si-empty-state p {
    margin: 0 0 25px 0;
    font-size: 14px;
    color: #5f5f5f;
    line-height: 1.5;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.si-empty-state .button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.si-empty-state .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Enhanced Template Modal Design */
.si-modal-large {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100000;
    padding: 20px;
    box-sizing: border-box;
}

.si-modal-large .si-modal-content {
    max-width: 800px;
    width: 100%;
    max-height: 90vh;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    display: flex;
    flex-direction: column;
    position: relative;
    margin: 0 auto;
}

.si-modal-large .si-modal-header {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: white;
    padding: 25px 30px;
    border-radius: 8px 8px 0 0;
    position: relative;
}

.si-modal-large .si-modal-header h2 {
    margin: 0;
    font-size: 22px;
    font-weight: 600;
    color: white;
}

.si-modal-large .si-modal-close {
    position: absolute;
    top: 20px;
    right: 25px;
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    font-size: 20px;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s ease;
}

.si-modal-large .si-modal-close:hover {
    background: rgba(255,255,255,0.3);
}

.si-modal-large .si-modal-body {
    padding: 30px;
    overflow-y: auto;
    flex: 1;
    min-height: 0;
}

/* Design Selector */
.si-design-selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.si-design-option {
    cursor: pointer;
    display: block;
    position: relative;
}

.si-design-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.si-design-preview {
    border: 2px solid #e7e7e7;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    background: #ffffff;
    transition: all 0.3s ease;
    position: relative;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.si-design-option:hover .si-design-preview {
    border-color: #f47a45;
    box-shadow: 0 4px 12px rgba(244,122,69,0.15);
    transform: translateY(-2px);
}

.si-design-option input[type="radio"]:checked + .si-design-preview {
    border-color: #f47a45;
    background: #ffffff;
    box-shadow: 0 0 0 1px #f47a45;
}

.si-design-option input[type="radio"]:checked + .si-design-preview::after {
    content: '✓';
    position: absolute;
    top: 10px;
    right: 10px;
    background: #f47a45;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
}

.si-design-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    color: #5f5f5f;
}

.si-design-placeholder .dashicons {
    font-size: 40px;
    width: 40px;
    height: 40px;
    color: #f47a45;
}

.si-design-name {
    font-weight: 600;
    color: #000000;
    font-size: 15px;
    margin-top: 8px;
}

/* Checkbox Grid */
.si-checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 12px;
    margin-top: 15px;
}

.si-checkbox-grid label {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    border: 1px solid #e7e7e7;
    border-radius: 6px;
    background: #ffffff;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
}

.si-checkbox-grid label:hover {
    border-color: #f47a45;
    background: #ffffff;
    box-shadow: 0 2px 8px rgba(244,122,69,0.1);
}

.si-checkbox-grid input[type="checkbox"]:checked + span {
    color: #f47a45;
    font-weight: 600;
}

.si-checkbox-grid input[type="checkbox"] {
    margin: 0;
    transform: scale(1.1);
}

/* Form Sections */
.si-form-section {
    margin-bottom: 35px;
    padding-bottom: 25px;
    border-bottom: 1px solid #e7e7e7;
}

.si-form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.si-form-section h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    color: #000000;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.si-form-section h3::before {
    content: '';
    width: 4px;
    height: 20px;
    background: #f47a45;
    border-radius: 2px;
}

.si-form-section .description {
    margin-bottom: 20px;
    color: #5f5f5f;
    font-size: 14px;
    line-height: 1.5;
}

/* Modal Footer */
.si-modal-large .si-modal-footer {
    padding: 20px 30px;
    background: #e7e7e7;
    border-top: 1px solid #e7e7e7;
    border-radius: 0 0 8px 8px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.si-modal-large .si-modal-footer .button {
    padding: 8px 16px;
    font-weight: 500;
}

.si-modal-large .si-modal-footer .button-primary {
    background: #f47a45;
    border-color: #f47a45;
}

.si-modal-large .si-modal-footer .button-primary:hover {
    background: #5f5f5f;
    border-color: #5f5f5f;
}

/* Removed complex step and form styles - using simple WordPress admin styling */

/* Removed complex design grid styles - using simple radio buttons */

/* Removed complex field configuration styles - using simple checkboxes */

/* Responsive Design for Template Modal */
@media (max-width: 768px) {
    .si-modal-large {
        padding: 15px;
    }

    .si-modal-large .si-modal-content {
        max-width: 100%;
        max-height: 95vh;
    }

    .si-modal-large .si-modal-header {
        padding: 20px;
    }

    .si-modal-large .si-modal-header h2 {
        font-size: 20px;
    }

    .si-modal-large .si-modal-body {
        padding: 20px;
    }

    .si-design-selector {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .si-checkbox-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .si-form-section {
        margin-bottom: 25px;
        padding-bottom: 20px;
    }

    .si-modal-large .si-modal-footer {
        padding: 15px 20px;
        flex-direction: column;
    }

    .si-modal-large .si-modal-footer .button {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .si-modal-large {
        padding: 10px;
    }

    .si-modal-large .si-modal-content {
        max-height: 98vh;
    }

    .si-modal-large .si-modal-header {
        padding: 15px;
    }

    .si-modal-large .si-modal-header h2 {
        font-size: 18px;
    }

    .si-modal-large .si-modal-body {
        padding: 15px;
        max-height: 60vh;
    }

    .si-design-preview {
        padding: 15px;
        min-height: 100px;
    }

    .si-checkbox-grid label {
        padding: 12px;
    }

    .si-form-section h3 {
        font-size: 16px;
    }
}

/* Filters Section - WordPress Admin Style */
.si-invoices-filters {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    margin: 20px 0;
    padding: 0;
    border-radius: 0;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.si-filter-row {
    display: grid;
    grid-template-columns: 1fr 180px 140px 140px 160px;
    gap: 15px;
    align-items: end;
    padding: 20px;
    background: #e7e7e7;
    border-bottom: 1px solid #e7e7e7;
}

.si-search-box,
.si-status-filter,
.si-date-from-filter,
.si-date-to-filter {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.si-search-box label,
.si-status-filter label,
.si-date-from-filter label,
.si-date-to-filter label {
    font-weight: 600;
    color: #000000;
    font-size: 13px;
    margin: 0;
    line-height: 1.3;
}

.si-search-box input,
.si-status-filter select,
.si-date-from-filter input,
.si-date-to-filter input {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #e7e7e7;
    border-radius: 0;
    font-size: 14px;
    background: #ffffff;
    color: #000000;
    line-height: 1.4;
    box-shadow: inset 0 1px 2px rgba(0,0,0,.07);
}

.si-search-box input:focus,
.si-status-filter select:focus,
.si-date-from-filter input:focus,
.si-date-to-filter input:focus {
    border-color: #f47a45;
    outline: 2px solid transparent;
    box-shadow: 0 0 0 1px #f47a45;
}

.si-filter-actions {
    display: flex;
    gap: 8px;
    align-self: end;
}

.si-filter-actions .button {
    padding: 6px 12px;
    font-weight: 400;
    font-size: 13px;
    height: auto;
    line-height: 1.4;
    white-space: nowrap;
    border-radius: 3px;
    text-decoration: none;
    border: 1px solid #f47a45;
    background: #f47a45;
    color: #fff;
    cursor: pointer;
}

.si-filter-actions .button:hover {
    background: #5f5f5f;
    border-color: #5f5f5f;
    color: #fff;
}

.si-filter-actions .button.button-secondary {
    background: #e7e7e7;
    border-color: #e7e7e7;
    color: #000000;
}

.si-filter-actions .button.button-secondary:hover {
    background: #ffffff;
    border-color: #5f5f5f;
    color: #000000;
}

.si-filter-actions .button .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    margin-right: 5px;
    vertical-align: text-top;
}

/* Table Wrapper - WordPress Admin Style */
.si-invoices-table-wrapper {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 0;
    overflow: hidden;
    margin-bottom: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.si-table-header {
    background: #e7e7e7;
    padding: 12px 20px;
    border-bottom: 1px solid #e7e7e7;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.si-table-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #000000;
    line-height: 1.4;
}

.si-table-info {
    color: #5f5f5f;
    font-size: 13px;
    line-height: 1.4;
}

/* WordPress-style Table */
.si-invoices-table {
    border: none;
    box-shadow: none;
    margin: 0;
    width: 100%;
    border-collapse: collapse;
    background: #fff;
}

.si-invoices-table th {
    background: #e7e7e7;
    border-bottom: 1px solid #e7e7e7;
    font-weight: 600;
    padding: 8px 10px;
    color: #000000;
    font-size: 13px;
    text-align: left;
    line-height: 1.4;
    vertical-align: middle;
}

.si-invoices-table td {
    padding: 10px;
    border-bottom: 1px solid #e7e7e7;
    vertical-align: middle;
    line-height: 1.4;
}

.si-invoice-row:hover {
    background: #e7e7e7;
}

.si-invoices-table tbody tr:nth-child(odd) {
    background: #e7e7e7;
}

.si-invoices-table tbody tr:nth-child(odd):hover {
    background: #e7e7e7;
}

/* Column Specific Styles - WordPress Admin Style */
.column-sr-no {
    width: 50px;
    text-align: center;
}

.si-serial-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    background: #e7e7e7;
    color: #5f5f5f;
    border-radius: 2px;
    font-weight: 500;
    font-size: 11px;
    line-height: 1;
}

.column-invoice-number {
    width: 150px;
}

.si-invoice-info {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.si-invoice-number {
    font-size: 14px;
    line-height: 1.4;
}

.si-invoice-link {
    color: #f47a45;
    text-decoration: none;
    font-weight: 600;
}

.si-invoice-link:hover {
    color: #5f5f5f;
    text-decoration: underline;
}

.si-invoice-meta {
    font-size: 12px;
    color: #5f5f5f;
    line-height: 1.3;
}

.column-client {
    width: 200px;
}

.si-client-info {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.si-client-name {
    font-weight: 600;
    color: #000000;
    font-size: 14px;
    line-height: 1.4;
}

.si-client-business {
    color: #5f5f5f;
    font-size: 12px;
    font-style: italic;
    line-height: 1.3;
}

.si-client-email {
    color: #5f5f5f;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
    line-height: 1.3;
}

.si-client-email .dashicons {
    font-size: 12px;
    width: 12px;
    height: 12px;
}

.column-date {
    width: 120px;
}

.si-date-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.si-date {
    font-weight: 600;
    color: #000000;
    font-size: 13px;
    line-height: 1.4;
}

.si-time {
    color: #5f5f5f;
    font-size: 12px;
    line-height: 1.3;
}

.column-amount {
    width: 130px;
    text-align: right;
}

.si-amount-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 3px;
    text-align: right;
}

.si-amount {
    font-weight: 600;
    color: #000000;
    font-size: 15px;
    text-align: right;
    width: 100%;
    line-height: 1.4;
}

.si-amount-meta {
    color: #5f5f5f;
    font-size: 12px;
    text-align: right;
    width: 100%;
    line-height: 1.3;
}

.column-status {
    width: 110px;
    text-align: center;
}

.si-status-wrapper {
    display: flex;
    justify-content: center;
}

.si-status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1.2;
    min-width: 60px;
    text-align: center;
}

.si-status-draft {
    background: #e7e7e7;
    color: #5f5f5f;
    border: 1px solid #e7e7e7;
}

.si-status-sent {
    background: #e7e7e7;
    color: #f47a45;
    border: 1px solid #e7e7e7;
}

.si-status-paid {
    background: #e7e7e7;
    color: #000000;
    border: 1px solid #e7e7e7;
}

.si-status-overdue {
    background: #e7e7e7;
    color: #f47a45;
    border: 1px solid #e7e7e7;
}

/* Empty State - WordPress Admin Style */
.si-empty-state {
    text-align: center;
    padding: 80px 40px;
    background: #ffffff;
}

.si-empty-icon {
    margin-bottom: 24px;
}

.si-empty-icon .dashicons {
    font-size: 72px;
    width: 72px;
    height: 72px;
    color: #5f5f5f;
}

.si-empty-state h3 {
    margin: 0 0 12px 0;
    font-size: 18px;
    color: #000000;
    font-weight: 600;
    line-height: 1.4;
}

.si-empty-state p {
    margin: 0 0 30px 0;
    color: #5f5f5f;
    font-size: 14px;
    line-height: 1.5;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.si-empty-state .button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    font-weight: 400;
    background: #f47a45;
    border-color: #f47a45;
    color: #fff;
}

/* Action Buttons - WordPress Admin Style */
.column-actions {
    width: 140px;
}

.si-invoice-actions {
    display: flex;
    align-items: center;
    gap: 6px;
    justify-content: center;
}

.si-primary-actions {
    display: flex;
    gap: 4px;
}

.si-secondary-actions {
    display: flex;
    gap: 4px;
}

.si-action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border: 1px solid #e7e7e7;
    border-radius: 3px;
    background: #e7e7e7;
    color: #5f5f5f;
    text-decoration: none;
    cursor: pointer;
    font-size: 0;
    transition: all 0.15s ease-in-out;
}

.si-action-btn:hover {
    background: #f47a45;
    color: #fff;
    border-color: #f47a45;
}

.si-action-btn .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.si-status-dropdown {
    position: relative;
    display: inline-block;
}

.si-status-menu {
    position: absolute;
    top: calc(100% + 4px);
    right: -40px;
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 3px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    z-index: 1000;
    min-width: 120px;
    overflow: hidden;
}

.si-status-option {
    display: block;
    padding: 8px 12px;
    text-decoration: none;
    color: #000000;
    font-size: 12px;
    border-bottom: 1px solid #e7e7e7;
}

.si-status-option:hover {
    background: #e7e7e7;
    color: #f47a45;
}

.si-status-option:last-child {
    border-bottom: none;
}

/* Pagination - WordPress Admin Style */
.si-pagination {
    margin: 20px 0;
    text-align: center;
    background: #e7e7e7;
    padding: 12px 20px;
    border-radius: 0;
    border: 1px solid #e7e7e7;
    border-top: none;
}

.si-pagination .page-numbers {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 28px;
    height: 28px;
    padding: 0 6px;
    margin: 0 1px;
    text-decoration: none;
    border: 1px solid #e7e7e7;
    border-radius: 3px;
    color: #f47a45;
    font-weight: 400;
    font-size: 13px;
    background: #ffffff;
    line-height: 1.4;
}

.si-pagination .page-numbers:hover {
    background: #e7e7e7;
    border-color: #f47a45;
    color: #5f5f5f;
}

.si-pagination .page-numbers.current {
    background: #f47a45;
    color: #fff;
    border-color: #f47a45;
}

.si-pagination .page-numbers.dots {
    border: none;
    background: none;
    color: #5f5f5f;
}

.si-loading {
    text-align: center;
    padding: 60px;
    color: #5f5f5f;
    font-size: 16px;
}

/* Modal styles for invoice view */
.si-modal-large .si-modal-content {
    max-width: 95%;
    width: 1200px;
}

.si-modal-large .si-modal-body {
    max-height: 75vh;
    overflow-y: auto;
    padding: 0;
}

/* Responsive Design - WordPress Admin Style */
@media (max-width: 1200px) {
    .si-filter-row {
        grid-template-columns: 1fr 160px 130px 130px 140px;
        gap: 12px;
    }

    /* Old stats wrapper styles removed */
}

@media (max-width: 768px) {
    /* Old stats wrapper styles removed */

    .si-filter-row {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 15px;
    }

    .si-search-box,
    .si-status-filter,
    .si-date-from-filter,
    .si-date-to-filter {
        gap: 8px;
    }

    .si-filter-actions {
        flex-direction: row;
        justify-content: flex-start;
        align-self: stretch;
        gap: 10px;
    }

    .si-filter-actions .button {
        flex: 1;
        min-height: 44px;
        padding: 10px 16px;
        font-size: 14px;
    }

    .si-table-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
        padding: 15px;
    }

    .si-invoices-table th,
    .si-invoices-table td {
        padding: 8px 6px;
        font-size: 12px;
    }

    .column-sr-no {
        width: 40px;
    }

    .column-invoice-number {
        width: 120px;
    }

    .column-client {
        width: 150px;
    }

    .column-date {
        width: 100px;
    }

    .column-amount {
        width: 100px;
    }

    .column-status {
        width: 90px;
    }

    .column-actions {
        width: 100px;
    }
}

@media (max-width: 480px) {
    /* Old stats wrapper styles removed */

    .si-filter-row {
        padding: 12px;
        gap: 12px;
    }

    .si-search-box input,
    .si-status-filter select,
    .si-date-from-filter input,
    .si-date-to-filter input {
        padding: 8px 10px;
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .si-filter-actions .button {
        min-height: 48px;
        font-size: 16px;
    }

    .si-invoices-table th,
    .si-invoices-table td {
        padding: 6px 4px;
        font-size: 11px;
    }

    .si-serial-number {
        width: 18px;
        height: 18px;
        line-height: 18px;
        font-size: 9px;
    }

    .si-action-btn {
        width: 24px;
        height: 24px;
    }

    .si-action-btn .dashicons {
        font-size: 12px;
        width: 12px;
        height: 12px;
    }

    .si-invoice-actions {
        flex-direction: column;
        gap: 4px;
    }

    .si-primary-actions,
    .si-secondary-actions {
        gap: 4px;
    }

    /* Hide less important columns on mobile */
    .column-date,
    .si-client-business,
    .si-client-email,
    .si-amount-meta {
        display: none;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .si-form-row {
        grid-template-columns: 1fr;
    }
    
    .si-templates-grid {
        grid-template-columns: 1fr;
    }
    
    .si-design-selector {
        grid-template-columns: 1fr 1fr;
    }
    
    .si-checkbox-grid {
        grid-template-columns: 1fr;
    }
    
    .si-summary-grid {
        grid-template-columns: 1fr;
        max-width: none;
    }
    
    .si-summary-row label {
        text-align: left;
    }
    
    .si-modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .si-field-row {
        flex-direction: column;
        align-items: stretch;
    }
}

/* Loading States */
.si-loading {
    opacity: 0.6;
    pointer-events: none;
}

.si-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #e7e7e7;
    border-top: 2px solid #f47a45;
    border-radius: 50%;
    animation: si-spin 1s linear infinite;
}

@keyframes si-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error Messages */
.si-message {
    padding: 10px 15px;
    border-radius: 4px;
    margin: 10px 0;
}

.si-message.success {
    background: #e7e7e7;
    color: #000000;
    border: 1px solid #e7e7e7;
}

.si-message.error {
    background: #e7e7e7;
    color: #f47a45;
    border: 1px solid #e7e7e7;
}

/* Dashboard Styles - WordPress Admin Style */
.si-dashboard {
    max-width: 1200px;
}

/* Welcome Panel */
.si-welcome-panel {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    margin: 20px 0;
    padding: 30px;
    position: relative;
    overflow: hidden;
}

.si-welcome-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #f47a45 0%, #5f5f5f 100%);
}

.si-welcome-content h2 {
    margin: 0 0 15px 0;
    font-size: 24px;
    font-weight: 600;
    color: #000000;
    line-height: 1.3;
}

.si-welcome-content .about-description {
    font-size: 16px;
    color: #5f5f5f;
    margin: 0 0 25px 0;
    line-height: 1.5;
}

.si-welcome-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.si-welcome-actions .button-hero {
    padding: 12px 24px;
    font-size: 16px;
    height: auto;
    line-height: 1.4;
}

.si-welcome-actions .button .dashicons {
    margin-right: 8px;
    font-size: 18px;
    width: 18px;
    height: 18px;
    vertical-align: text-top;
}

/* Dashboard Stats */
.si-dashboard-stats {
    margin: 30px 0;
}

.si-dashboard-stats h2 {
    margin: 0 0 20px 0;
    font-size: 20px;
    font-weight: 600;
    color: #000000;
}

.si-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.si-stat-card {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    transition: all 0.2s ease;
}

.si-stat-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,.1);
    transform: translateY(-1px);
}

.si-stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.si-stat-icon .dashicons {
    font-size: 24px;
    width: 24px;
    height: 24px;
    color: #ffffff;
}

.si-stat-revenue .si-stat-icon,
.si-stat-templates .si-stat-icon {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
}

.si-stat-monthly .si-stat-icon,
.si-stat-designs .si-stat-icon {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
}

.si-stat-invoices .si-stat-icon,
.si-stat-popular .si-stat-icon {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
}

.si-stat-clients .si-stat-icon,
.si-stat-recent .si-stat-icon {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
}

.si-stat-content {
    flex: 1;
}

.si-stat-number {
    font-size: 28px;
    font-weight: 700;
    color: #000000;
    line-height: 1.2;
    margin-bottom: 4px;
}

.si-stat-label {
    font-size: 14px;
    font-weight: 600;
    color: #5f5f5f;
    margin-bottom: 2px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.si-stat-meta {
    font-size: 12px;
    color: #5f5f5f;
}

/* Status Breakdown */
.si-dashboard-breakdown {
    margin: 30px 0;
}

.si-dashboard-breakdown h2 {
    margin: 0 0 20px 0;
    font-size: 20px;
    font-weight: 600;
    color: #000000;
}

.si-status-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.si-status-card {
    text-align: center;
}

.si-status-number {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 8px;
    line-height: 1;
}

.si-status-label {
    font-size: 13px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 12px;
}

.si-status-bar {
    height: 6px;
    background: #e7e7e7;
    border-radius: 3px;
    overflow: hidden;
}

.si-status-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 0.3s ease;
}

.si-status-paid .si-status-number {
    color: #000000;
}

.si-status-paid .si-status-label {
    color: #5f5f5f;
}

.si-status-paid .si-status-fill {
    background: linear-gradient(90deg, #f47a45 0%, #5f5f5f 100%);
}

.si-status-sent .si-status-number {
    color: #000000;
}

.si-status-sent .si-status-label {
    color: #5f5f5f;
}

.si-status-sent .si-status-fill {
    background: linear-gradient(90deg, #f47a45 0%, #5f5f5f 100%);
}

.si-status-draft .si-status-number {
    color: #000000;
}

.si-status-draft .si-status-label {
    color: #5f5f5f;
}

.si-status-draft .si-status-fill {
    background: linear-gradient(90deg, #f47a45 0%, #5f5f5f 100%);
}

.si-status-overdue .si-status-number {
    color: #000000;
}

.si-status-overdue .si-status-label {
    color: #5f5f5f;
}

.si-status-overdue .si-status-fill {
    background: linear-gradient(90deg, #f47a45 0%, #5f5f5f 100%);
}

/* Dashboard Columns */
.si-dashboard-columns {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin: 30px 0;
}

.si-dashboard-column {
    min-width: 0; /* Prevent overflow */
}

/* Dashboard Widgets */
.si-dashboard-widget {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    overflow: hidden;
}

.si-widget-header {
    background: #e7e7e7;
    border-bottom: 1px solid #e7e7e7;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.si-widget-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #000000;
}

.si-widget-action {
    color: #f47a45;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
}

.si-widget-action:hover {
    color: #5f5f5f;
    text-decoration: underline;
}

.si-widget-content {
    padding: 20px;
}

/* Recent Invoices */
.si-recent-invoices {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.si-recent-invoice {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: #e7e7e7;
    border-radius: 6px;
    border: 1px solid #e7e7e7;
    transition: all 0.2s ease;
}

.si-recent-invoice:hover {
    background: #ffffff;
    border-color: #e7e7e7;
}

.si-invoice-info {
    flex: 1;
}

.si-invoice-number {
    font-size: 14px;
    margin-bottom: 4px;
}

.si-invoice-number strong {
    color: #f47a45;
    font-weight: 600;
}

.si-invoice-client {
    font-size: 13px;
    color: #5f5f5f;
    margin-bottom: 2px;
}

.si-invoice-date {
    font-size: 12px;
    color: #5f5f5f;
}

.si-invoice-amount {
    text-align: right;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 6px;
}

.si-amount {
    font-size: 15px;
    font-weight: 600;
    color: #000000;
}

.si-status-badge {
    font-size: 10px;
    font-weight: 500;
    padding: 3px 8px;
    border-radius: 10px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

/* Quick Actions */
.si-quick-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.si-quick-action {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: #e7e7e7;
    border: 1px solid #e7e7e7;
    border-radius: 6px;
    text-decoration: none;
    color: inherit;
    transition: all 0.2s ease;
}

.si-quick-action:hover {
    background: #ffffff;
    border-color: #e7e7e7;
    color: inherit;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,.1);
}

.si-action-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: #f47a45;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-size: 18px;
}

.si-action-content {
    flex: 1;
}

.si-action-title {
    font-size: 14px;
    font-weight: 600;
    color: #000000;
    margin-bottom: 4px;
}

.si-action-desc {
    font-size: 12px;
    color: #5f5f5f;
    line-height: 1.4;
}

/* Empty State */
.si-widget-content .si-empty-state {
    text-align: center;
    padding: 40px 20px;
}

.si-widget-content .si-empty-state p {
    color: #5f5f5f;
    margin-bottom: 20px;
    font-size: 14px;
}

/* Dashboard Responsive Design */
@media (max-width: 1024px) {
    .si-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .si-dashboard-columns {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .si-status-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .si-welcome-panel {
        padding: 20px;
    }

    .si-welcome-content h2 {
        font-size: 20px;
    }

    .si-welcome-content .about-description {
        font-size: 14px;
    }

    .si-welcome-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .si-welcome-actions .button {
        text-align: center;
        justify-content: center;
    }

    .si-stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .si-stat-card {
        padding: 20px;
    }

    .si-stat-number {
        font-size: 24px;
    }

    .si-status-stats {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 20px;
    }

    .si-dashboard-columns {
        gap: 15px;
    }

    .si-widget-content {
        padding: 15px;
    }

    .si-recent-invoice {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .si-invoice-amount {
        align-items: flex-start;
        text-align: left;
    }

    .si-quick-action {
        padding: 12px;
        gap: 12px;
    }

    .si-action-icon {
        width: 36px;
        height: 36px;
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .si-welcome-panel {
        padding: 15px;
        margin: 15px 0;
    }

    .si-welcome-content h2 {
        font-size: 18px;
    }

    .si-welcome-actions .button-hero {
        padding: 10px 20px;
        font-size: 14px;
    }

    .si-stat-card {
        padding: 16px;
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .si-stat-icon {
        width: 40px;
        height: 40px;
    }

    .si-stat-icon .dashicons {
        font-size: 20px;
        width: 20px;
        height: 20px;
    }

    .si-stat-number {
        font-size: 20px;
    }

    .si-widget-header {
        padding: 12px 15px;
    }

    .si-widget-content {
        padding: 12px;
    }

    .si-recent-invoice {
        padding: 12px;
    }

    .si-quick-action {
        padding: 10px;
        gap: 10px;
    }

    .si-action-icon {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }

    .si-action-title {
        font-size: 13px;
    }

    .si-action-desc {
        font-size: 11px;
    }
}

/* Settings Page Styles */
.si-settings-wrap {
    max-width: 1000px;
}

.si-settings-wrap .page-title-action {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    text-decoration: none;
    color: #f47a45;
    font-weight: 500;
}

.si-settings-wrap .page-title-action:hover {
    color: #5f5f5f;
}

.si-settings-wrap .page-title-action .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Settings Navigation */
.si-settings-nav {
    margin: 20px 0;
}

.si-settings-nav .nav-tab-wrapper {
    border-bottom: 1px solid #e7e7e7;
    margin: 0;
    padding: 0;
}

.si-settings-nav .nav-tab {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    margin: 0 4px -1px 0;
    background: #e7e7e7;
    border: 1px solid #e7e7e7;
    border-bottom: none;
    color: #5f5f5f;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    border-radius: 4px 4px 0 0;
    transition: all 0.2s ease;
}

.si-settings-nav .nav-tab:hover {
    background: #ffffff;
    color: #f47a45;
}

.si-settings-nav .nav-tab.nav-tab-active {
    background: #ffffff;
    color: #000000;
    border-bottom: 1px solid #ffffff;
    margin-bottom: -1px;
    position: relative;
    z-index: 1;
}

.si-settings-nav .nav-tab .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Settings Container */
.si-settings-container {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-top: none;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    min-height: 500px;
}

.si-settings-tab {
    padding: 30px;
}

/* Section Headers */
.si-section-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e7e7e7;
}

.si-section-header h2 {
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 600;
    color: #000000;
    line-height: 1.3;
}

.si-section-header h2 .dashicons {
    font-size: 24px;
    width: 24px;
    height: 24px;
    color: #f47a45;
}

.si-section-description {
    margin: 0;
    color: #5f5f5f;
    font-size: 14px;
    line-height: 1.5;
}

.si-settings-section {
    background: transparent;
    border: none;
    border-radius: 0;
    margin: 0;
    box-shadow: none;
    overflow: visible;
}

/* Form Table Styles */
.si-settings-section .form-table {
    margin: 0;
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,.05);
}

.si-settings-section .form-table th {
    padding: 20px 24px;
    width: 220px;
    font-weight: 600;
    color: #000000;
    background: #e7e7e7;
    border-bottom: 1px solid #e7e7e7;
    vertical-align: top;
    font-size: 14px;
}

.si-settings-section .form-table td {
    padding: 20px 24px;
    border-bottom: 1px solid #e7e7e7;
    background: #ffffff;
}

.si-settings-section .form-table tr:last-child th,
.si-settings-section .form-table tr:last-child td {
    border-bottom: none;
}

.si-logo-preview {
    margin-top: 10px;
    padding: 10px;
    background: #e7e7e7;
    border: 1px solid #e7e7e7;
    border-radius: 4px;
    display: inline-block;
}

.si-media-button {
    margin-left: 10px;
    vertical-align: top;
}

/* Form Field Improvements */
.si-settings-section input[type="text"],
.si-settings-section input[type="email"],
.si-settings-section input[type="url"],
.si-settings-section input[type="number"],
.si-settings-section textarea,
.si-settings-section select {
    border: 1px solid #e7e7e7;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    line-height: 1.4;
    color: #000000;
    background: #ffffff;
}

.si-settings-section input[type="text"]:focus,
.si-settings-section input[type="email"]:focus,
.si-settings-section input[type="url"]:focus,
.si-settings-section input[type="number"]:focus,
.si-settings-section textarea:focus,
.si-settings-section select:focus {
    border-color: #f47a45;
    box-shadow: 0 0 0 1px #f47a45;
    outline: 2px solid transparent;
}

.si-settings-section .description {
    color: #5f5f5f;
    font-size: 13px;
    font-style: normal;
    margin: 8px 0 0 0;
    line-height: 1.4;
}

.si-settings-section fieldset {
    border: none;
    padding: 0;
    margin: 0;
}

.si-settings-section fieldset label {
    display: block;
    margin-bottom: 8px;
    font-weight: 400;
    color: #000000;
}

.si-settings-section fieldset input[type="checkbox"] {
    margin-right: 8px;
}

/* Save Button Section */
.si-settings-save {
    background: #ffffff;
    border-top: 1px solid #e7e7e7;
    padding: 20px 30px;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 15px;
    border-radius: 0 0 8px 8px;
}

.si-settings-save .button-primary {
    background: #f47a45;
    border-color: #f47a45;
    color: #fff;
    padding: 10px 24px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,.1);
    transition: all 0.2s ease;
}

.si-settings-save .button-primary:hover {
    background: #5f5f5f;
    border-color: #5f5f5f;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,.15);
}

.si-settings-save .button-primary:disabled {
    background: #5f5f5f;
    border-color: #5f5f5f;
    transform: none;
    box-shadow: none;
    cursor: not-allowed;
}

.si-save-status {
    font-size: 13px;
    font-weight: 500;
}

.si-save-status.success {
    color: #000000;
}

.si-save-status.error {
    color: #f47a45;
}

.si-save-status.warning {
    color: #f47a45;
}

/* Enhanced Logo Preview */
.si-logo-preview {
    margin-top: 15px;
    padding: 15px;
    background: #e7e7e7;
    border: 2px dashed #e7e7e7;
    border-radius: 8px;
    display: inline-block;
    text-align: center;
}

.si-logo-preview img {
    max-width: 200px;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,.1);
}

.si-media-button {
    margin-left: 12px;
    vertical-align: top;
    background: #e7e7e7;
    border-color: #e7e7e7;
    color: #000000;
    font-weight: 500;
}

.si-media-button:hover {
    background: #ffffff;
    border-color: #5f5f5f;
    color: #000000;
}

/* Settings Responsive Design */
@media (max-width: 1024px) {
    .si-settings-wrap {
        max-width: 100%;
        margin-right: 20px;
    }
}

@media (max-width: 768px) {
    .si-settings-nav .nav-tab {
        padding: 10px 12px;
        font-size: 13px;
        margin-right: 2px;
    }

    .si-settings-nav .nav-tab .dashicons {
        display: none;
    }

    .si-settings-tab {
        padding: 20px;
    }

    .si-section-header {
        margin-bottom: 20px;
        padding-bottom: 15px;
    }

    .si-section-header h2 {
        font-size: 18px;
    }

    .si-settings-section .form-table th,
    .si-settings-section .form-table td {
        display: block;
        width: 100%;
        padding: 15px 20px;
    }

    .si-settings-section .form-table th {
        padding-bottom: 8px;
        border-bottom: none;
        background: #ffffff;
        font-size: 13px;
    }

    .si-settings-section .form-table td {
        padding-top: 0;
        border-bottom: 1px solid #e7e7e7;
    }

    .si-settings-save {
        padding: 15px 20px;
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .si-settings-save .button-primary {
        width: 100%;
        text-align: center;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .si-settings-nav .nav-tab-wrapper {
        display: flex;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .si-settings-nav .nav-tab {
        flex-shrink: 0;
        white-space: nowrap;
        padding: 8px 10px;
        font-size: 12px;
    }

    .si-settings-tab {
        padding: 15px;
    }

    .si-section-header h2 {
        font-size: 16px;
        gap: 8px;
    }

    .si-section-header h2 .dashicons {
        font-size: 20px;
        width: 20px;
        height: 20px;
    }

    .si-settings-section .form-table th,
    .si-settings-section .form-table td {
        padding: 12px 15px;
    }

    .si-settings-section input[type="text"],
    .si-settings-section input[type="email"],
    .si-settings-section input[type="url"],
    .si-settings-section input[type="number"],
    .si-settings-section textarea,
    .si-settings-section select {
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .si-media-button {
        margin-left: 0;
        margin-top: 8px;
        width: 100%;
    }

    .si-logo-preview {
        margin-top: 10px;
        padding: 10px;
    }
}

/* Modern Clients Page Styles */
.si-clients-modern-wrap {
    max-width: 1400px;
    margin: 0;
}

/* Page Header */
.si-page-header {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    margin: 0 -20px 30px -20px;
    padding: 40px 20px;
    color: white;
    position: relative;
    overflow: hidden;
}

.si-page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.si-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.si-header-title {
    display: flex;
    align-items: center;
    gap: 20px;
}

.si-title-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    backdrop-filter: blur(10px);
}

.si-title-text h1 {
    margin: 0 0 8px 0;
    font-size: 32px;
    font-weight: 700;
    color: white;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.si-title-text p {
    margin: 0;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 400;
}

.si-header-actions {
    display: flex;
    gap: 12px;
}

/* Modern Button Styles */
.si-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    line-height: 1.4;
    white-space: nowrap;
}

.si-btn-primary {
    background: white;
    color: #f47a45;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.si-btn-primary:hover {
    background: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.2);
    color: #5f5f5f;
    text-decoration: none;
}

.si-btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.si-btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
    color: white;
    text-decoration: none;
}

.si-btn-ghost {
    background: transparent;
    color: #5f5f5f;
    border: 1px solid #e7e7e7;
}

.si-btn-ghost:hover {
    background: #e7e7e7;
    border-color: #5f5f5f;
    color: #000000;
    text-decoration: none;
}

.si-btn-danger {
    background: #f47a45;
    color: white;
}

.si-btn-danger:hover {
    background: #5f5f5f;
    transform: translateY(-1px);
    color: white;
    text-decoration: none;
}

/* Modern Statistics Dashboard */
.si-stats-dashboard {
    margin: 0 0 40px 0;
}

.si-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
}

.si-stat-card {
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.si-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--stat-color-start), var(--stat-color-end));
}

.si-stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.si-stat-primary {
    --stat-color-start: #f47a45;
    --stat-color-end: #5f5f5f;
}

.si-stat-success {
    --stat-color-start: #f47a45;
    --stat-color-end: #5f5f5f;
}

.si-stat-revenue {
    --stat-color-start: #f47a45;
    --stat-color-end: #5f5f5f;
}

.si-stat-info {
    --stat-color-start: #f47a45;
    --stat-color-end: #5f5f5f;
}

.si-stat-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.si-stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--stat-color-start), var(--stat-color-end));
    color: white;
}

.si-stat-trend {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.si-trend-up {
    background: #e7e7e7;
    color: #000000;
}

.si-trend-down {
    background: #e7e7e7;
    color: #f47a45;
}

.si-trend-neutral {
    background: #e7e7e7;
    color: #5f5f5f;
}

.si-stat-content {
    margin-top: 8px;
}

.si-stat-number {
    font-size: 28px;
    font-weight: 800;
    color: #000000;
    line-height: 1.2;
    margin-bottom: 4px;
}

.si-stat-label {
    font-size: 14px;
    font-weight: 600;
    color: #5f5f5f;
    margin-bottom: 4px;
}

.si-stat-meta {
    font-size: 12px;
    color: #5f5f5f;
    line-height: 1.4;
}

/* Modern Toolbar */
.si-toolbar {
    background: white;
    border-radius: 12px;
    padding: 20px 24px;
    margin: 0 0 32px 0;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.si-toolbar-left {
    display: flex;
    align-items: center;
    gap: 20px;
    flex: 1;
}

.si-toolbar-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

/* Search Container */
.si-search-container {
    flex: 1;
    max-width: 400px;
}

.si-search-form {
    margin: 0;
}

.si-search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.si-search-icon {
    position: absolute;
    left: 12px;
    color: #9ca3af;
    z-index: 1;
}

.si-search-input {
    width: 100%;
    padding: 12px 16px 12px 44px;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    font-size: 14px;
    background: #f9fafb;
    color: #111827;
    transition: all 0.2s ease;
}

.si-search-input:focus {
    outline: none;
    border-color: #3b82f6;
    background: white;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.si-search-input::placeholder {
    color: #9ca3af;
}

.si-search-clear {
    position: absolute;
    right: 12px;
    color: #9ca3af;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
    text-decoration: none;
}

.si-search-clear:hover {
    color: #ef4444;
    background: #fef2f2;
    text-decoration: none;
}

/* Search Results Info */
.si-search-results-info {
    display: flex;
    align-items: center;
}

.si-results-badge {
    background: #dbeafe;
    color: #1e40af;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

/* View Switcher */
.si-view-switcher {
    display: flex;
    background: #f3f4f6;
    border-radius: 8px;
    padding: 4px;
}

.si-view-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 6px;
    color: #6b7280;
    text-decoration: none;
    transition: all 0.2s ease;
}

.si-view-btn:hover {
    color: #374151;
    background: #e5e7eb;
    text-decoration: none;
}

.si-view-btn.active {
    background: white;
    color: #3b82f6;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* Toolbar Actions */
.si-toolbar-actions {
    display: flex;
    gap: 12px;
}

/* Modern Clients Container */
.si-clients-container {
    margin-bottom: 32px;
}

/* Grid View */
.si-clients-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 24px;
}

.si-client-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    position: relative;
}

.si-client-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.si-client-card.deleting {
    opacity: 0.5;
    transform: scale(0.95);
}

.si-card-header {
    padding: 20px 20px 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.si-client-avatar-large {
    width: 56px;
    height: 56px;
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 20px;
    text-transform: uppercase;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.si-avatar-text {
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.si-card-actions {
    position: relative;
}

/* Dropdown */
.si-dropdown {
    position: relative;
}

.si-dropdown-trigger {
    width: 36px;
    height: 36px;
    border: none;
    background: #f3f4f6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
}

.si-dropdown-trigger:hover {
    background: #e5e7eb;
    color: #374151;
}

.si-dropdown.active .si-dropdown-trigger {
    background: #3b82f6;
    color: white;
}

.si-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    min-width: 180px;
    z-index: 50;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: all 0.2s ease;
    margin-top: 8px;
}

.si-dropdown.active .si-dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.si-dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: #374151;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    transition: all 0.2s ease;
}

.si-dropdown-item:hover {
    background: #f3f4f6;
    color: #111827;
    text-decoration: none;
}

.si-dropdown-item:first-child {
    border-radius: 8px 8px 0 0;
}

.si-dropdown-item:last-child {
    border-radius: 0 0 8px 8px;
}

.si-dropdown-danger:hover {
    background: #fef2f2;
    color: #dc2626;
}

.si-card-body {
    padding: 16px 20px;
}

.si-client-name h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 700;
    color: #111827;
    line-height: 1.3;
}

.si-client-business {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 12px;
}

.si-client-contact {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.si-contact-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #6b7280;
}

.si-contact-item a {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
}

.si-contact-item a:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

.si-card-footer {
    padding: 16px 20px 20px 20px;
    border-top: 1px solid #f3f4f6;
    background: #f9fafb;
}

.si-client-stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 12px;
}

.si-stat-mini {
    text-align: center;
    padding: 12px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.si-stat-mini-number {
    font-size: 16px;
    font-weight: 700;
    color: #111827;
    line-height: 1.2;
}

.si-stat-mini-label {
    font-size: 11px;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 2px;
}

.si-last-activity {
    text-align: center;
    font-size: 12px;
    color: #6b7280;
}

.si-activity-label {
    font-weight: 500;
}

.si-activity-date {
    font-weight: 600;
    color: #3b82f6;
}

.si-no-activity {
    color: #9ca3af;
    font-style: italic;
}

/* List View */
.si-clients-list {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.si-list-header {
    display: grid;
    grid-template-columns: 2fr 1.5fr 1.5fr 1fr 120px;
    gap: 16px;
    padding: 16px 20px;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
}

.si-list-header-cell {
    font-size: 12px;
    font-weight: 700;
    color: #374151;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.si-list-body {
    divide-y: 1px solid #f3f4f6;
}

.si-list-row {
    display: grid;
    grid-template-columns: 2fr 1.5fr 1.5fr 1fr 120px;
    gap: 16px;
    padding: 16px 20px;
    align-items: center;
    transition: all 0.2s ease;
}

.si-list-row:hover {
    background: #f9fafb;
}

.si-list-row.deleting {
    opacity: 0.5;
    background: #fef2f2;
}

.si-list-cell {
    display: flex;
    align-items: center;
}

/* Compact Client Info */
.si-client-info-compact {
    display: flex;
    align-items: center;
    gap: 12px;
}

.si-client-avatar-small {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    flex-shrink: 0;
}

.si-client-details-compact {
    min-width: 0;
}

.si-client-name-compact strong {
    color: #111827;
    font-size: 14px;
    font-weight: 600;
    line-height: 1.3;
}

.si-client-meta-compact {
    color: #6b7280;
    font-size: 12px;
    margin-top: 2px;
}

/* Compact Contact Info */
.si-contact-info-compact {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.si-contact-item-compact {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    color: #6b7280;
}

.si-contact-item-compact a {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
}

.si-contact-item-compact a:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

/* Compact Business Info */
.si-business-info-compact {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.si-business-name-compact,
.si-gstin-compact {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    color: #374151;
    font-weight: 500;
}

/* Compact Stats */
.si-client-stats-compact {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.si-stat-compact {
    display: flex;
    align-items: center;
    gap: 6px;
}

.si-stat-number-compact {
    font-size: 14px;
    font-weight: 700;
    color: #111827;
}

.si-stat-label-compact {
    font-size: 11px;
    font-weight: 500;
    color: #6b7280;
}

/* Compact Actions */
.si-client-actions-compact {
    display: flex;
    gap: 4px;
    justify-content: center;
}

.si-action-btn-compact {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    background: #f9fafb;
    color: #6b7280;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.si-action-btn-compact:hover {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
    text-decoration: none;
}

.si-action-btn-compact.si-action-danger:hover {
    background: #ef4444;
    border-color: #ef4444;
}

/* Empty Field Styling */
.si-empty-field-compact {
    color: #9ca3af;
    font-style: italic;
    font-size: 12px;
}

/* Additional Modal Styles - Removed complex modern styles for simplicity */

/* Simplified Form Styles - Removed complex modern styles */

/* Removed complex form styles - using standard WordPress form styling */

/* Removed complex modern styles - using standard WordPress styling */

/* Modern Responsive Design */
@media (max-width: 1200px) {
    .si-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .si-clients-grid {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    }
}

@media (max-width: 1024px) {
    .si-page-header {
        padding: 32px 20px;
    }

    .si-header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .si-title-text h1 {
        font-size: 28px;
    }

    .si-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .si-toolbar {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .si-toolbar-left {
        flex-direction: column;
        gap: 16px;
    }

    .si-toolbar-right {
        justify-content: space-between;
    }

    .si-search-container {
        max-width: none;
    }

    .si-clients-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
    }

    .si-list-header,
    .si-list-row {
        grid-template-columns: 2fr 1fr 100px;
        gap: 12px;
    }

    .si-col-business,
    .si-col-stats {
        display: none;
    }

    .si-pagination-modern {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }
}

@media (max-width: 768px) {
    .si-clients-modern-wrap {
        margin: 0 -10px;
    }

    .si-page-header {
        margin: 0 -10px 20px -10px;
        padding: 24px 20px;
    }

    .si-title-icon {
        width: 48px;
        height: 48px;
    }

    .si-title-text h1 {
        font-size: 24px;
    }

    .si-title-text p {
        font-size: 14px;
    }

    .si-btn {
        padding: 10px 20px;
        font-size: 13px;
    }

    .si-stats-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .si-stat-card {
        padding: 20px;
    }

    .si-stat-number {
        font-size: 24px;
    }

    .si-toolbar {
        padding: 16px 20px;
        margin: 0 0 24px 0;
    }

    .si-search-input {
        padding: 10px 14px 10px 40px;
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .si-clients-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .si-client-card {
        margin: 0 10px;
    }

    .si-card-header {
        padding: 16px 16px 0 16px;
    }

    .si-client-avatar-large {
        width: 48px;
        height: 48px;
        font-size: 18px;
    }

    .si-card-body {
        padding: 12px 16px;
    }

    .si-card-footer {
        padding: 12px 16px 16px 16px;
    }

    .si-list-header,
    .si-list-row {
        grid-template-columns: 1fr 80px;
        gap: 8px;
        padding: 12px 16px;
    }

    .si-col-contact {
        display: none;
    }

    .si-client-info-compact {
        gap: 8px;
    }

    .si-client-avatar-small {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }

    /* Modal responsive */
    .si-modal-container {
        margin: 10px;
    }

    .si-modal-header-modern {
        padding: 24px 24px 20px 24px;
    }

    .si-modal-title-text h2 {
        font-size: 20px;
    }

    .si-modal-body-modern {
        padding: 24px;
    }

    /* Removed old form grid styles */

    .si-modal-footer-modern {
        padding: 20px 24px 24px 24px;
        flex-direction: column;
        gap: 12px;
    }

    .si-footer-left,
    .si-footer-right {
        width: 100%;
        justify-content: center;
    }

    .si-footer-right {
        flex-direction: column-reverse;
    }

    .si-btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .si-page-header {
        padding: 20px 16px;
    }

    .si-title-icon {
        width: 40px;
        height: 40px;
    }

    .si-title-text h1 {
        font-size: 20px;
    }

    .si-title-text p {
        font-size: 13px;
    }

    .si-btn {
        padding: 8px 16px;
        font-size: 12px;
    }

    .si-stat-card {
        padding: 16px;
    }

    .si-stat-icon {
        width: 40px;
        height: 40px;
    }

    .si-stat-number {
        font-size: 20px;
    }

    .si-toolbar {
        padding: 12px 16px;
    }

    .si-search-input {
        padding: 8px 12px 8px 36px;
    }

    .si-view-switcher {
        padding: 2px;
    }

    .si-view-btn {
        width: 32px;
        height: 32px;
    }

    .si-client-card {
        margin: 0 5px;
    }

    .si-card-header {
        padding: 12px 12px 0 12px;
    }

    .si-client-avatar-large {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .si-card-body {
        padding: 8px 12px;
    }

    .si-client-name h3 {
        font-size: 16px;
    }

    .si-card-footer {
        padding: 8px 12px 12px 12px;
    }

    .si-client-stats-grid {
        gap: 8px;
    }

    .si-stat-mini {
        padding: 8px;
    }

    .si-stat-mini-number {
        font-size: 14px;
    }

    .si-list-row {
        padding: 8px 12px;
    }

    .si-client-avatar-small {
        width: 28px;
        height: 28px;
        font-size: 10px;
    }

    .si-action-btn-compact {
        width: 28px;
        height: 28px;
    }

    /* Modal mobile */
    .si-modal-container {
        margin: 5px;
    }

    .si-modal-content-modern {
        border-radius: 12px;
    }

    .si-modal-header-modern {
        padding: 20px 20px 16px 20px;
    }

    .si-modal-close-modern {
        top: 16px;
        right: 16px;
        width: 32px;
        height: 32px;
    }

    .si-modal-title-text h2 {
        font-size: 18px;
    }

    .si-modal-body-modern {
        padding: 20px;
    }

    .si-form-steps {
        margin-bottom: 24px;
        gap: 12px;
    }

    /* Removed old step styles */

    .si-input-modern,
    .si-textarea-modern {
        padding: 10px 14px 10px 40px;
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .si-modal-footer-modern {
        padding: 16px 20px 20px 20px;
    }

    .si-empty-state-modern {
        padding: 40px 20px;
    }

    .si-empty-content h3 {
        font-size: 18px;
    }

    .si-empty-content p {
        font-size: 14px;
    }

    .si-empty-actions {
        flex-direction: column;
    }

    .si-confirm-content {
        padding: 24px;
        width: 95%;
    }

    .si-confirm-content h3 {
        font-size: 16px;
    }

    .si-confirm-actions {
        flex-direction: column;
    }

    /* Plugin Settings Responsive */
    .si-section-title-wrapper {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .si-section-icon {
        width: 40px;
        height: 40px;
        align-self: center;
    }

    .si-setting-header {
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }

    .si-setting-icon {
        width: 32px;
        height: 32px;
        align-self: center;
    }

    .si-toggle-switch {
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }

    .si-setting-warning {
        flex-direction: column;
        gap: 6px;
        text-align: center;
    }

    .si-plugin-settings-save .si-btn {
        width: 100%;
        justify-content: center;
    }
}

/* Clear Data Tab Styles */
.si-data-overview {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
}

.si-data-overview h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #1d2327;
}

.si-data-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 20px;
}

.si-data-stats .si-stat-item {
    text-align: center;
    padding: 16px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.si-data-stats .si-stat-number {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: #2271b1;
    line-height: 1.2;
    margin-bottom: 4px;
}

.si-data-stats .si-stat-label {
    font-size: 12px;
    font-weight: 600;
    color: #646970;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.si-clear-data-form {
    background: white;
    border: 1px solid #c3c4c7;
    border-radius: 8px;
    padding: 24px;
}

.si-clear-options h3 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #1d2327;
}

.si-clear-options .description {
    margin-bottom: 24px;
    color: #646970;
    font-size: 14px;
    line-height: 1.5;
}

.si-clear-checkboxes {
    display: grid;
    gap: 16px;
    margin-bottom: 32px;
}

.si-clear-option {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    transition: all 0.2s ease;
    background: #f8f9fa;
}

.si-clear-option:hover {
    border-color: #c3c4c7;
    background: #f0f0f1;
}

.si-clear-option:has(input:checked) {
    border-color: #dc3545;
    background: #fff5f5;
}

.si-clear-label {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    cursor: pointer;
    margin: 0;
    font-weight: normal;
}

.si-clear-label input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.si-checkbox-custom {
    width: 20px;
    height: 20px;
    border: 2px solid #8c8f94;
    border-radius: 4px;
    background: white;
    position: relative;
    flex-shrink: 0;
    margin-top: 2px;
    transition: all 0.2s ease;
}

.si-clear-label input:checked + .si-checkbox-custom {
    background: #dc3545;
    border-color: #dc3545;
}

.si-clear-label input:checked + .si-checkbox-custom::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.si-option-content {
    flex: 1;
}

.si-option-content strong {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #1d2327;
    margin-bottom: 4px;
}

.si-option-description {
    font-size: 13px;
    color: #646970;
    line-height: 1.4;
}

.si-clear-actions {
    border-top: 1px solid #e9ecef;
    padding-top: 24px;
}

.si-clear-warning {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    margin-bottom: 20px;
    color: #856404;
    font-size: 14px;
}

.si-clear-warning .dashicons {
    color: #f39c12;
    font-size: 18px;
    width: 18px;
    height: 18px;
    flex-shrink: 0;
}

.si-clear-buttons {
    display: flex;
    gap: 12px;
    align-items: center;
}

#si-clear-data-btn {
    background: #dc3545;
    border-color: #dc3545;
    color: white;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

#si-clear-data-btn:hover:not(:disabled) {
    background: #c82333;
    border-color: #bd2130;
}

#si-clear-data-btn:disabled {
    background: #6c757d;
    border-color: #6c757d;
    opacity: 0.6;
    cursor: not-allowed;
}

#si-clear-data-btn .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.si-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive Design for Clear Data Tab */
@media (max-width: 768px) {
    .si-data-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    .si-data-stats .si-stat-item {
        padding: 12px;
    }

    .si-data-stats .si-stat-number {
        font-size: 20px;
    }

    .si-clear-data-form {
        padding: 16px;
    }

    .si-clear-buttons {
        flex-direction: column;
        align-items: stretch;
    }

    .si-clear-buttons .button {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .si-data-stats {
        grid-template-columns: 1fr;
    }

    .si-clear-option {
        padding: 12px;
    }

    .si-clear-label {
        gap: 8px;
    }

    .si-option-content strong {
        font-size: 13px;
    }

    .si-option-description {
        font-size: 12px;
    }

    .si-clear-warning {
        padding: 8px 12px;
        font-size: 13px;
    }
}

/* Modern Plugin Settings Section */
.si-plugin-settings-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 32px;
    overflow: hidden;
}

.si-section-title-wrapper {
    display: flex;
    align-items: center;
    gap: 16px;
}

.si-section-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.si-section-title-content h2 {
    margin: 0 0 4px 0;
    font-size: 20px;
    font-weight: 700;
    color: #111827;
}

.si-section-title-content .si-section-description {
    margin: 0;
    font-size: 14px;
    color: #6b7280;
    line-height: 1.5;
}

.si-plugin-settings-content {
    padding: 0 24px 24px 24px;
}

.si-setting-card {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.si-setting-header {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 16px;
}

.si-setting-icon {
    width: 40px;
    height: 40px;
    background: #dbeafe;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #3b82f6;
    flex-shrink: 0;
}

.si-setting-info h3 {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
    color: #111827;
}

.si-setting-info p {
    margin: 0;
    font-size: 14px;
    color: #6b7280;
    line-height: 1.4;
}

.si-setting-control {
    margin-bottom: 16px;
}

/* Modern Toggle Switch */
.si-toggle-switch {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    user-select: none;
}

.si-toggle-switch input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.si-toggle-slider {
    position: relative;
    width: 48px;
    height: 24px;
    background: #d1d5db;
    border-radius: 24px;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.si-toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.si-toggle-switch input:checked + .si-toggle-slider {
    background: #3b82f6;
}

.si-toggle-switch input:checked + .si-toggle-slider::before {
    transform: translateX(24px);
}

.si-toggle-label {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    line-height: 1.4;
}

/* Warning Section */
.si-setting-warning {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    padding: 12px 16px;
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 6px;
    font-size: 13px;
    line-height: 1.4;
}

.si-warning-icon {
    color: #d97706;
    flex-shrink: 0;
    margin-top: 1px;
}

.si-warning-content {
    color: #92400e;
}

.si-warning-content strong {
    color: #78350f;
    font-weight: 600;
}

/* Plugin Settings Save Button */
.si-plugin-settings-save {
    padding: 20px 24px 24px 24px;
    border-top: 1px solid #e5e7eb;
    background: #f9fafb;
}

.si-plugin-settings-save .si-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.si-plugin-settings-save .si-btn:hover {
    background: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    color: white;
    text-decoration: none;
}

.si-plugin-settings-save .si-btn:active {
    transform: translateY(0);
}

/* Clear Data Notifications */
.si-clear-notification {
    margin: 0 0 20px 0;
    border-left: 4px solid;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.si-clear-notification.notice-success {
    border-left-color: #00a32a;
    background: #f0f8f0;
}

.si-clear-notification.notice-error {
    border-left-color: #d63638;
    background: #fdf0f0;
}

.si-clear-notification p {
    margin: 0.5em 0;
    font-weight: 500;
}

/* Loading state improvements */
.si-clear-data-form.loading {
    opacity: 0.7;
    pointer-events: none;
}

.si-clear-data-form.loading .si-clear-checkboxes {
    filter: blur(1px);
}

/* Utility Classes */
body.si-modal-open {
    overflow: hidden;
}

.si-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Print Styles */
@media print {
    .si-modal-header,
    .si-modal-footer,
    .no-print {
        display: none !important;
    }

    .si-modal-content {
        box-shadow: none;
        border: none;
        max-width: none;
        width: 100%;
        margin: 0;
    }

    .si-modal-body {
        padding: 0;
    }
}
